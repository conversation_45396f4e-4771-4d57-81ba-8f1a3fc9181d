"""
Technical indicators module for trading analysis.
Provides common technical analysis indicators used in trading strategies.
"""

import pandas as pd
import numpy as np
from typing import Tuple, Optional, Dict, Any
import warnings

warnings.filterwarnings('ignore')


class TechnicalIndicators:
    """Collection of technical analysis indicators."""
    
    @staticmethod
    def sma(data: pd.Series, period: int) -> pd.Series:
        """
        Simple Moving Average.
        
        Args:
            data: Price series
            period: Moving average period
            
        Returns:
            SMA series
        """
        return data.rolling(window=period).mean()
    
    @staticmethod
    def ema(data: pd.Series, period: int) -> pd.Series:
        """
        Exponential Moving Average.
        
        Args:
            data: Price series
            period: EMA period
            
        Returns:
            EMA series
        """
        return data.ewm(span=period).mean()
    
    @staticmethod
    def rsi(data: pd.Series, period: int = 14) -> pd.Series:
        """
        Relative Strength Index.
        
        Args:
            data: Price series
            period: RSI period (default: 14)
            
        Returns:
            RSI series
        """
        delta = data.diff()
        gains = delta.where(delta > 0, 0)
        losses = -delta.where(delta < 0, 0)
        
        avg_gains = gains.rolling(window=period).mean()
        avg_losses = losses.rolling(window=period).mean()
        
        rs = avg_gains / avg_losses
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    @staticmethod
    def macd(data: pd.Series, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9) -> Dict[str, pd.Series]:
        """
        MACD (Moving Average Convergence Divergence).
        
        Args:
            data: Price series
            fast_period: Fast EMA period (default: 12)
            slow_period: Slow EMA period (default: 26)
            signal_period: Signal line EMA period (default: 9)
            
        Returns:
            Dictionary with 'macd', 'signal', and 'histogram' series
        """
        ema_fast = TechnicalIndicators.ema(data, fast_period)
        ema_slow = TechnicalIndicators.ema(data, slow_period)
        
        macd_line = ema_fast - ema_slow
        signal_line = TechnicalIndicators.ema(macd_line, signal_period)
        histogram = macd_line - signal_line
        
        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }
    
    @staticmethod
    def bollinger_bands(data: pd.Series, period: int = 20, std_dev: float = 2.0) -> Dict[str, pd.Series]:
        """
        Bollinger Bands.
        
        Args:
            data: Price series
            period: Moving average period (default: 20)
            std_dev: Standard deviation multiplier (default: 2.0)
            
        Returns:
            Dictionary with 'upper', 'middle', and 'lower' bands
        """
        middle_band = TechnicalIndicators.sma(data, period)
        std = data.rolling(window=period).std()
        
        upper_band = middle_band + (std * std_dev)
        lower_band = middle_band - (std * std_dev)
        
        return {
            'upper': upper_band,
            'middle': middle_band,
            'lower': lower_band
        }
    
    @staticmethod
    def stochastic(high: pd.Series, low: pd.Series, close: pd.Series, k_period: int = 14, d_period: int = 3) -> Dict[str, pd.Series]:
        """
        Stochastic Oscillator.
        
        Args:
            high: High price series
            low: Low price series
            close: Close price series
            k_period: %K period (default: 14)
            d_period: %D period (default: 3)
            
        Returns:
            Dictionary with '%K' and '%D' series
        """
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_period).mean()
        
        return {
            '%K': k_percent,
            '%D': d_percent
        }
    
    @staticmethod
    def williams_r(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """
        Williams %R.
        
        Args:
            high: High price series
            low: Low price series
            close: Close price series
            period: Lookback period (default: 14)
            
        Returns:
            Williams %R series
        """
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        
        williams_r = -100 * ((highest_high - close) / (highest_high - lowest_low))
        
        return williams_r
    
    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """
        Average True Range.
        
        Args:
            high: High price series
            low: Low price series
            close: Close price series
            period: ATR period (default: 14)
            
        Returns:
            ATR series
        """
        prev_close = close.shift(1)
        
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    @staticmethod
    def adx(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> Dict[str, pd.Series]:
        """
        Average Directional Index.
        
        Args:
            high: High price series
            low: Low price series
            close: Close price series
            period: ADX period (default: 14)
            
        Returns:
            Dictionary with 'ADX', '+DI', and '-DI' series
        """
        # Calculate True Range
        atr_values = TechnicalIndicators.atr(high, low, close, period)
        
        # Calculate directional movements
        dm_plus = high.diff()
        dm_minus = -low.diff()
        
        # Set negative values to 0
        dm_plus = dm_plus.where(dm_plus > 0, 0)
        dm_minus = dm_minus.where(dm_minus > 0, 0)
        
        # Calculate smoothed directional movements
        dm_plus_smooth = dm_plus.rolling(window=period).mean()
        dm_minus_smooth = dm_minus.rolling(window=period).mean()
        
        # Calculate directional indicators
        di_plus = 100 * (dm_plus_smooth / atr_values)
        di_minus = 100 * (dm_minus_smooth / atr_values)
        
        # Calculate ADX
        dx = 100 * abs(di_plus - di_minus) / (di_plus + di_minus)
        adx = dx.rolling(window=period).mean()
        
        return {
            'ADX': adx,
            '+DI': di_plus,
            '-DI': di_minus
        }
    
    @staticmethod
    def ichimoku_cloud(high: pd.Series, low: pd.Series, close: pd.Series, 
                      tenkan_period: int = 9, kijun_period: int = 26, 
                      senkou_span_b_period: int = 52, displacement: int = 26) -> Dict[str, pd.Series]:
        """
        Ichimoku Cloud components.
        
        Args:
            high: High price series
            low: Low price series
            close: Close price series
            tenkan_period: Tenkan-sen period (default: 9)
            kijun_period: Kijun-sen period (default: 26)
            senkou_span_b_period: Senkou Span B period (default: 52)
            displacement: Cloud displacement (default: 26)
            
        Returns:
            Dictionary with Ichimoku components
        """
        # Tenkan-sen (Conversion Line)
        tenkan_high = high.rolling(window=tenkan_period).max()
        tenkan_low = low.rolling(window=tenkan_period).min()
        tenkan_sen = (tenkan_high + tenkan_low) / 2
        
        # Kijun-sen (Base Line)
        kijun_high = high.rolling(window=kijun_period).max()
        kijun_low = low.rolling(window=kijun_period).min()
        kijun_sen = (kijun_high + kijun_low) / 2
        
        # Senkou Span A (Leading Span A)
        senkou_span_a = ((tenkan_sen + kijun_sen) / 2).shift(displacement)
        
        # Senkou Span B (Leading Span B)
        senkou_high = high.rolling(window=senkou_span_b_period).max()
        senkou_low = low.rolling(window=senkou_span_b_period).min()
        senkou_span_b = ((senkou_high + senkou_low) / 2).shift(displacement)
        
        # Chikou Span (Lagging Span)
        chikou_span = close.shift(-displacement)
        
        return {
            'tenkan_sen': tenkan_sen,
            'kijun_sen': kijun_sen,
            'senkou_span_a': senkou_span_a,
            'senkou_span_b': senkou_span_b,
            'chikou_span': chikou_span
        }
    
    @staticmethod
    def fibonacci_retracement(high_price: float, low_price: float) -> Dict[str, float]:
        """
        Calculate Fibonacci retracement levels.
        
        Args:
            high_price: Highest price in the range
            low_price: Lowest price in the range
            
        Returns:
            Dictionary with Fibonacci levels
        """
        diff = high_price - low_price
        
        levels = {
            '0%': high_price,
            '23.6%': high_price - (diff * 0.236),
            '38.2%': high_price - (diff * 0.382),
            '50%': high_price - (diff * 0.5),
            '61.8%': high_price - (diff * 0.618),
            '78.6%': high_price - (diff * 0.786),
            '100%': low_price
        }
        
        return levels
    
    @staticmethod
    def pivot_points(high: float, low: float, close: float) -> Dict[str, float]:
        """
        Calculate pivot points and support/resistance levels.
        
        Args:
            high: Previous period high
            low: Previous period low
            close: Previous period close
            
        Returns:
            Dictionary with pivot points and levels
        """
        pivot = (high + low + close) / 3
        
        # Support levels
        s1 = (2 * pivot) - high
        s2 = pivot - (high - low)
        s3 = low - 2 * (high - pivot)
        
        # Resistance levels
        r1 = (2 * pivot) - low
        r2 = pivot + (high - low)
        r3 = high + 2 * (pivot - low)
        
        return {
            'pivot': pivot,
            'r1': r1,
            'r2': r2,
            'r3': r3,
            's1': s1,
            's2': s2,
            's3': s3
        }
    
    @staticmethod
    def volume_weighted_average_price(high: pd.Series, low: pd.Series, 
                                    close: pd.Series, volume: pd.Series) -> pd.Series:
        """
        Volume Weighted Average Price (VWAP).
        
        Args:
            high: High price series
            low: Low price series
            close: Close price series
            volume: Volume series
            
        Returns:
            VWAP series
        """
        typical_price = (high + low + close) / 3
        vwap = (typical_price * volume).cumsum() / volume.cumsum()
        
        return vwap
    
    @staticmethod
    def money_flow_index(high: pd.Series, low: pd.Series, close: pd.Series, 
                        volume: pd.Series, period: int = 14) -> pd.Series:
        """
        Money Flow Index.
        
        Args:
            high: High price series
            low: Low price series
            close: Close price series
            volume: Volume series
            period: MFI period (default: 14)
            
        Returns:
            MFI series
        """
        typical_price = (high + low + close) / 3
        money_flow = typical_price * volume
        
        # Positive and negative money flow
        positive_flow = money_flow.where(typical_price > typical_price.shift(1), 0)
        negative_flow = money_flow.where(typical_price < typical_price.shift(1), 0)
        
        # Money flow ratio
        positive_mf = positive_flow.rolling(window=period).sum()
        negative_mf = negative_flow.rolling(window=period).sum()
        
        money_ratio = positive_mf / negative_mf
        mfi = 100 - (100 / (1 + money_ratio))
        
        return mfi


class IndicatorCalculator:
    """Helper class for calculating multiple indicators on OHLCV data."""
    
    def __init__(self, data: pd.DataFrame):
        """
        Initialize with OHLCV data.
        
        Args:
            data: DataFrame with columns ['open', 'high', 'low', 'close', 'volume']
        """
        self.data = data.copy()
        self.indicators = TechnicalIndicators()
    
    def add_all_indicators(self) -> pd.DataFrame:
        """
        Add all common indicators to the dataframe.
        
        Returns:
            DataFrame with all indicators added
        """
        df = self.data.copy()
        
        # Moving averages
        df['SMA_10'] = self.indicators.sma(df['close'], 10)
        df['SMA_20'] = self.indicators.sma(df['close'], 20)
        df['SMA_50'] = self.indicators.sma(df['close'], 50)
        df['EMA_12'] = self.indicators.ema(df['close'], 12)
        df['EMA_26'] = self.indicators.ema(df['close'], 26)
        
        # RSI
        df['RSI'] = self.indicators.rsi(df['close'])
        
        # MACD
        macd_data = self.indicators.macd(df['close'])
        df['MACD'] = macd_data['macd']
        df['MACD_Signal'] = macd_data['signal']
        df['MACD_Histogram'] = macd_data['histogram']
        
        # Bollinger Bands
        bb_data = self.indicators.bollinger_bands(df['close'])
        df['BB_Upper'] = bb_data['upper']
        df['BB_Middle'] = bb_data['middle']
        df['BB_Lower'] = bb_data['lower']
        
        # Stochastic
        stoch_data = self.indicators.stochastic(df['high'], df['low'], df['close'])
        df['Stoch_K'] = stoch_data['%K']
        df['Stoch_D'] = stoch_data['%D']
        
        # ATR
        df['ATR'] = self.indicators.atr(df['high'], df['low'], df['close'])
        
        # Williams %R
        df['Williams_R'] = self.indicators.williams_r(df['high'], df['low'], df['close'])
        
        return df
    
    def get_latest_values(self) -> Dict[str, float]:
        """
        Get latest values of all indicators.
        
        Returns:
            Dictionary with latest indicator values
        """
        df_with_indicators = self.add_all_indicators()
        latest_row = df_with_indicators.iloc[-1]
        
        return {
            'price': latest_row['close'],
            'sma_10': latest_row['SMA_10'],
            'sma_20': latest_row['SMA_20'],
            'sma_50': latest_row['SMA_50'],
            'ema_12': latest_row['EMA_12'],
            'ema_26': latest_row['EMA_26'],
            'rsi': latest_row['RSI'],
            'macd': latest_row['MACD'],
            'macd_signal': latest_row['MACD_Signal'],
            'bb_upper': latest_row['BB_Upper'],
            'bb_middle': latest_row['BB_Middle'],
            'bb_lower': latest_row['BB_Lower'],
            'stoch_k': latest_row['Stoch_K'],
            'stoch_d': latest_row['Stoch_D'],
            'atr': latest_row['ATR'],
            'williams_r': latest_row['Williams_R']
        }
