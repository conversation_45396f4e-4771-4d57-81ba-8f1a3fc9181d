"""
Bollinger Bands Strategy.
Generates buy/sell signals based on price interaction with Bollinger Bands.
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import List, Dict, Any

from .base_strategy import BaseStrategy, Signal
from ..indicators import TechnicalIndicators


class BollingerBandsStrategy(BaseStrategy):
    """
    Bollinger Bands trading strategy.
    
    Generates BUY signal when price touches lower band and shows reversal.
    Generates SELL signal when price touches upper band and shows reversal.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize Bollinger Bands strategy.
        
        Args:
            config: Strategy configuration with parameters:
                - bb_period: Bollinger Bands period (default: 20)
                - bb_std_dev: Standard deviation multiplier (default: 2.0)
                - rsi_period: RSI period for confirmation (default: 14)
                - rsi_oversold: RSI oversold level (default: 30)
                - rsi_overbought: RSI overbought level (default: 70)
                - min_signal_strength: Minimum strength for signal (default: 0.6)
        """
        default_config = {
            'bb_period': 20,
            'bb_std_dev': 2.0,
            'rsi_period': 14,
            'rsi_oversold': 30,
            'rsi_overbought': 70,
            'min_signal_strength': 0.6,
            'max_position_size': 0.1,
            'stop_loss_percentage': 0.025,
            'take_profit_percentage': 0.05
        }
        
        if config:
            default_config.update(config)
        
        super().__init__("Bollinger Bands", default_config)
        
        self.bb_period = self.config['bb_period']
        self.bb_std_dev = self.config['bb_std_dev']
        self.rsi_period = self.config['rsi_period']
        self.rsi_oversold = self.config['rsi_oversold']
        self.rsi_overbought = self.config['rsi_overbought']
        self.min_signal_strength = self.config['min_signal_strength']
    
    def get_required_indicators(self) -> List[str]:
        """Get required indicators for this strategy."""
        return [
            f'BB_Upper_{self.bb_period}',
            f'BB_Middle_{self.bb_period}',
            f'BB_Lower_{self.bb_period}',
            f'RSI_{self.rsi_period}'
        ]
    
    def get_minimum_data_length(self) -> int:
        """Get minimum data length required."""
        return max(self.bb_period * 2, 50)
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate Bollinger Bands and RSI indicators.
        
        Args:
            data: OHLCV DataFrame
            
        Returns:
            DataFrame with indicators added
        """
        df = data.copy()
        
        # Calculate Bollinger Bands
        bb_data = TechnicalIndicators.bollinger_bands(
            df['close'], self.bb_period, self.bb_std_dev
        )
        df['BB_Upper'] = bb_data['upper']
        df['BB_Middle'] = bb_data['middle']
        df['BB_Lower'] = bb_data['lower']
        
        # Calculate RSI for confirmation
        df['RSI'] = TechnicalIndicators.rsi(df['close'], self.rsi_period)
        
        # Calculate Bollinger Band width and position
        df['BB_Width'] = (df['BB_Upper'] - df['BB_Lower']) / df['BB_Middle']
        df['BB_Position'] = (df['close'] - df['BB_Lower']) / (df['BB_Upper'] - df['BB_Lower'])
        
        return df
    
    def detect_band_touch(self, data: pd.DataFrame) -> tuple:
        """
        Detect when price touches or penetrates Bollinger Bands.
        
        Args:
            data: DataFrame with BB indicators
            
        Returns:
            Tuple of (touch_type, strength) where:
            - touch_type: 'upper_touch', 'lower_touch', or 'none'
            - strength: Touch strength (0.0 to 1.0)
        """
        if len(data) < 3:
            return 'none', 0.0
        
        current_price = data['close'].iloc[-1]
        prev_price = data['close'].iloc[-2]
        
        current_upper = data['BB_Upper'].iloc[-1]
        current_lower = data['BB_Lower'].iloc[-1]
        current_middle = data['BB_Middle'].iloc[-1]
        
        # Check for lower band touch (potential buy signal)
        if current_price <= current_lower:
            # Calculate penetration depth
            penetration = (current_lower - current_price) / current_lower
            
            # Check for reversal (price moving back up)
            reversal = current_price > prev_price
            
            if reversal:
                strength = min(0.7 + penetration * 5, 1.0)
                return 'lower_touch', strength
            else:
                strength = min(0.4 + penetration * 3, 0.8)
                return 'lower_touch', strength
        
        # Check for upper band touch (potential sell signal)
        elif current_price >= current_upper:
            # Calculate penetration depth
            penetration = (current_price - current_upper) / current_upper
            
            # Check for reversal (price moving back down)
            reversal = current_price < prev_price
            
            if reversal:
                strength = min(0.7 + penetration * 5, 1.0)
                return 'upper_touch', strength
            else:
                strength = min(0.4 + penetration * 3, 0.8)
                return 'upper_touch', strength
        
        return 'none', 0.0
    
    def calculate_bb_squeeze(self, data: pd.DataFrame) -> float:
        """
        Calculate Bollinger Band squeeze indicator.
        
        Args:
            data: DataFrame with BB indicators
            
        Returns:
            Squeeze strength (0.0 to 1.0)
        """
        if len(data) < 20:
            return 0.0
        
        # Calculate current BB width
        current_width = data['BB_Width'].iloc[-1]
        
        # Calculate average BB width over last 20 periods
        avg_width = data['BB_Width'].tail(20).mean()
        
        # Squeeze occurs when current width is significantly below average
        if current_width < avg_width * 0.8:
            squeeze_strength = (avg_width - current_width) / avg_width
            return min(squeeze_strength * 2, 1.0)
        
        return 0.0
    
    def calculate_mean_reversion_strength(self, data: pd.DataFrame) -> float:
        """
        Calculate mean reversion strength based on distance from middle band.
        
        Args:
            data: DataFrame with BB indicators
            
        Returns:
            Mean reversion strength (0.0 to 1.0)
        """
        current_price = data['close'].iloc[-1]
        current_middle = data['BB_Middle'].iloc[-1]
        current_upper = data['BB_Upper'].iloc[-1]
        current_lower = data['BB_Lower'].iloc[-1]
        
        # Calculate distance from middle band as percentage of band width
        band_width = current_upper - current_lower
        distance_from_middle = abs(current_price - current_middle)
        
        if band_width == 0:
            return 0.0
        
        distance_ratio = distance_from_middle / (band_width / 2)
        
        # Higher distance means stronger mean reversion potential
        return min(distance_ratio, 1.0)
    
    def get_rsi_confirmation(self, data: pd.DataFrame, signal_type: str) -> float:
        """
        Get RSI confirmation for BB signals.
        
        Args:
            data: DataFrame with RSI
            signal_type: 'buy' or 'sell'
            
        Returns:
            Confirmation strength (0.0 to 1.0)
        """
        current_rsi = data['RSI'].iloc[-1]
        
        if signal_type == 'buy':
            # For buy signals, prefer oversold RSI
            if current_rsi <= self.rsi_oversold:
                return 1.0
            elif current_rsi <= 40:
                return 0.7
            elif current_rsi <= 50:
                return 0.4
            else:
                return 0.2
        
        elif signal_type == 'sell':
            # For sell signals, prefer overbought RSI
            if current_rsi >= self.rsi_overbought:
                return 1.0
            elif current_rsi >= 60:
                return 0.7
            elif current_rsi >= 50:
                return 0.4
            else:
                return 0.2
        
        return 0.0
    
    def analyze(self, data: pd.DataFrame) -> Signal:
        """
        Analyze market data and generate trading signal.
        
        Args:
            data: OHLCV DataFrame
            
        Returns:
            Signal object with trading recommendation
        """
        if not self.validate_data(data):
            return Signal(
                action='HOLD',
                strength=0.0,
                price=data['close'].iloc[-1] if len(data) > 0 else 0.0,
                timestamp=datetime.now(),
                reason="Insufficient or invalid data"
            )
        
        # Calculate indicators
        df_with_indicators = self.calculate_indicators(data)
        
        # Check if we have enough data after indicator calculation
        if (df_with_indicators['BB_Upper'].isna().any() or 
            df_with_indicators['RSI'].isna().any()):
            return Signal(
                action='HOLD',
                strength=0.0,
                price=data['close'].iloc[-1],
                timestamp=datetime.now(),
                reason="Insufficient data for indicator calculation"
            )
        
        # Detect band touches
        touch_type, touch_strength = self.detect_band_touch(df_with_indicators)
        
        current_price = data['close'].iloc[-1]
        current_bb_upper = df_with_indicators['BB_Upper'].iloc[-1]
        current_bb_middle = df_with_indicators['BB_Middle'].iloc[-1]
        current_bb_lower = df_with_indicators['BB_Lower'].iloc[-1]
        current_rsi = df_with_indicators['RSI'].iloc[-1]
        
        # Calculate additional factors
        squeeze_strength = self.calculate_bb_squeeze(df_with_indicators)
        mean_reversion_strength = self.calculate_mean_reversion_strength(df_with_indicators)
        
        # Generate signals based on analysis
        if touch_type == 'lower_touch':
            rsi_confirmation = self.get_rsi_confirmation(df_with_indicators, 'buy')
            
            # Combine all factors for final signal strength
            final_strength = (
                touch_strength * 0.4 +
                rsi_confirmation * 0.3 +
                mean_reversion_strength * 0.2 +
                squeeze_strength * 0.1
            )
            
            if final_strength >= self.min_signal_strength:
                signal = Signal(
                    action='BUY',
                    strength=final_strength,
                    price=current_price,
                    timestamp=datetime.now(),
                    reason=f"Lower BB touch with reversal. Price: {current_price:.2f}, Lower BB: {current_bb_lower:.2f}",
                    metadata={
                        'bb_upper': current_bb_upper,
                        'bb_middle': current_bb_middle,
                        'bb_lower': current_bb_lower,
                        'rsi': current_rsi,
                        'touch_strength': touch_strength,
                        'rsi_confirmation': rsi_confirmation,
                        'mean_reversion_strength': mean_reversion_strength,
                        'squeeze_strength': squeeze_strength
                    }
                )
            else:
                signal = Signal(
                    action='HOLD',
                    strength=final_strength,
                    price=current_price,
                    timestamp=datetime.now(),
                    reason=f"Lower BB touch but insufficient signal strength: {final_strength:.2f}",
                    metadata={'final_strength': final_strength}
                )
        
        elif touch_type == 'upper_touch':
            rsi_confirmation = self.get_rsi_confirmation(df_with_indicators, 'sell')
            
            # Combine all factors for final signal strength
            final_strength = (
                touch_strength * 0.4 +
                rsi_confirmation * 0.3 +
                mean_reversion_strength * 0.2 +
                squeeze_strength * 0.1
            )
            
            if final_strength >= self.min_signal_strength:
                signal = Signal(
                    action='SELL',
                    strength=final_strength,
                    price=current_price,
                    timestamp=datetime.now(),
                    reason=f"Upper BB touch with reversal. Price: {current_price:.2f}, Upper BB: {current_bb_upper:.2f}",
                    metadata={
                        'bb_upper': current_bb_upper,
                        'bb_middle': current_bb_middle,
                        'bb_lower': current_bb_lower,
                        'rsi': current_rsi,
                        'touch_strength': touch_strength,
                        'rsi_confirmation': rsi_confirmation,
                        'mean_reversion_strength': mean_reversion_strength,
                        'squeeze_strength': squeeze_strength
                    }
                )
            else:
                signal = Signal(
                    action='HOLD',
                    strength=final_strength,
                    price=current_price,
                    timestamp=datetime.now(),
                    reason=f"Upper BB touch but insufficient signal strength: {final_strength:.2f}",
                    metadata={'final_strength': final_strength}
                )
        
        else:
            # No band touch
            signal = Signal(
                action='HOLD',
                strength=0.0,
                price=current_price,
                timestamp=datetime.now(),
                reason=f"No significant BB signal. Price: {current_price:.2f} between bands",
                metadata={
                    'bb_upper': current_bb_upper,
                    'bb_middle': current_bb_middle,
                    'bb_lower': current_bb_lower,
                    'rsi': current_rsi,
                    'bb_position': df_with_indicators['BB_Position'].iloc[-1]
                }
            )
        
        # Add signal to history
        self.signals_history.append(signal)
        
        # Keep only last 100 signals in history
        if len(self.signals_history) > 100:
            self.signals_history = self.signals_history[-100:]
        
        return signal
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """Get strategy information and current parameters."""
        return {
            'name': self.name,
            'bb_period': self.bb_period,
            'bb_std_dev': self.bb_std_dev,
            'rsi_period': self.rsi_period,
            'rsi_oversold': self.rsi_oversold,
            'rsi_overbought': self.rsi_overbought,
            'min_signal_strength': self.min_signal_strength,
            'active_positions': len(self.positions),
            'total_signals': len(self.signals_history),
            'performance': self.get_performance_metrics()
        }
