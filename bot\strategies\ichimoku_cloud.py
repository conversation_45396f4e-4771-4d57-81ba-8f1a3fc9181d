"""
Ichimoku Cloud Strategy.
Generates buy/sell signals based on Ichimoku Kinko Hyo components.
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import List, Dict, Any

from .base_strategy import BaseStrategy, Signal
from ..indicators import TechnicalIndicators


class IchimokuCloudStrategy(BaseStrategy):
    """
    Ichimoku Cloud trading strategy.
    
    Generates signals based on:
    - Price position relative to cloud
    - Tenkan-sen and Kijun-sen crossovers
    - Chikou Span confirmation
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize Ichimoku Cloud strategy.
        
        Args:
            config: Strategy configuration with parameters:
                - tenkan_period: Tenkan-sen period (default: 9)
                - kijun_period: Kijun-sen period (default: 26)
                - senkou_span_b_period: Senkou Span B period (default: 52)
                - displacement: Cloud displacement (default: 26)
                - min_signal_strength: Minimum strength for signal (default: 0.6)
        """
        default_config = {
            'tenkan_period': 9,
            'kijun_period': 26,
            'senkou_span_b_period': 52,
            'displacement': 26,
            'min_signal_strength': 0.6,
            'max_position_size': 0.1,
            'stop_loss_percentage': 0.03,
            'take_profit_percentage': 0.06
        }
        
        if config:
            default_config.update(config)
        
        super().__init__("Ichimoku Cloud", default_config)
        
        self.tenkan_period = self.config['tenkan_period']
        self.kijun_period = self.config['kijun_period']
        self.senkou_span_b_period = self.config['senkou_span_b_period']
        self.displacement = self.config['displacement']
        self.min_signal_strength = self.config['min_signal_strength']
    
    def get_required_indicators(self) -> List[str]:
        """Get required indicators for this strategy."""
        return [
            'Tenkan_Sen',
            'Kijun_Sen',
            'Senkou_Span_A',
            'Senkou_Span_B',
            'Chikou_Span'
        ]
    
    def get_minimum_data_length(self) -> int:
        """Get minimum data length required."""
        return max(self.senkou_span_b_period + self.displacement, 100)
    
    def calculate_ichimoku_components(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate all Ichimoku components.
        
        Args:
            data: OHLCV DataFrame
            
        Returns:
            DataFrame with Ichimoku components added
        """
        df = data.copy()
        
        # Calculate Ichimoku components
        ichimoku_data = TechnicalIndicators.ichimoku_cloud(
            df['high'], df['low'], df['close'],
            self.tenkan_period, self.kijun_period,
            self.senkou_span_b_period, self.displacement
        )
        
        df['Tenkan_Sen'] = ichimoku_data['tenkan_sen']
        df['Kijun_Sen'] = ichimoku_data['kijun_sen']
        df['Senkou_Span_A'] = ichimoku_data['senkou_span_a']
        df['Senkou_Span_B'] = ichimoku_data['senkou_span_b']
        df['Chikou_Span'] = ichimoku_data['chikou_span']
        
        # Calculate cloud top and bottom
        df['Cloud_Top'] = df[['Senkou_Span_A', 'Senkou_Span_B']].max(axis=1)
        df['Cloud_Bottom'] = df[['Senkou_Span_A', 'Senkou_Span_B']].min(axis=1)
        
        # Calculate cloud thickness
        df['Cloud_Thickness'] = df['Cloud_Top'] - df['Cloud_Bottom']
        
        return df
    
    def analyze_price_cloud_position(self, data: pd.DataFrame) -> tuple:
        """
        Analyze price position relative to the cloud.
        
        Args:
            data: DataFrame with Ichimoku components
            
        Returns:
            Tuple of (position, strength) where:
            - position: 'above_cloud', 'below_cloud', 'in_cloud'
            - strength: Position strength (0.0 to 1.0)
        """
        current_price = data['close'].iloc[-1]
        current_cloud_top = data['Cloud_Top'].iloc[-1]
        current_cloud_bottom = data['Cloud_Bottom'].iloc[-1]
        
        if pd.isna(current_cloud_top) or pd.isna(current_cloud_bottom):
            return 'unknown', 0.0
        
        if current_price > current_cloud_top:
            # Price above cloud - bullish
            distance = (current_price - current_cloud_top) / current_cloud_top
            strength = min(distance * 10, 1.0)
            return 'above_cloud', strength
        
        elif current_price < current_cloud_bottom:
            # Price below cloud - bearish
            distance = (current_cloud_bottom - current_price) / current_cloud_bottom
            strength = min(distance * 10, 1.0)
            return 'below_cloud', strength
        
        else:
            # Price in cloud - neutral/consolidation
            cloud_thickness = current_cloud_top - current_cloud_bottom
            if cloud_thickness > 0:
                position_in_cloud = (current_price - current_cloud_bottom) / cloud_thickness
                strength = 0.5 - abs(position_in_cloud - 0.5)  # Lower strength when in cloud
            else:
                strength = 0.0
            return 'in_cloud', strength
    
    def detect_tenkan_kijun_crossover(self, data: pd.DataFrame) -> tuple:
        """
        Detect Tenkan-sen and Kijun-sen crossovers.
        
        Args:
            data: DataFrame with Ichimoku components
            
        Returns:
            Tuple of (crossover_type, strength)
        """
        if len(data) < 2:
            return 'none', 0.0
        
        current_tenkan = data['Tenkan_Sen'].iloc[-1]
        current_kijun = data['Kijun_Sen'].iloc[-1]
        prev_tenkan = data['Tenkan_Sen'].iloc[-2]
        prev_kijun = data['Kijun_Sen'].iloc[-2]
        
        if pd.isna(current_tenkan) or pd.isna(current_kijun):
            return 'none', 0.0
        
        # Bullish crossover: Tenkan crosses above Kijun
        if prev_tenkan <= prev_kijun and current_tenkan > current_kijun:
            separation = abs(current_tenkan - current_kijun) / current_kijun
            strength = min(0.7 + separation * 10, 1.0)
            return 'bullish_tk', strength
        
        # Bearish crossover: Tenkan crosses below Kijun
        elif prev_tenkan >= prev_kijun and current_tenkan < current_kijun:
            separation = abs(current_tenkan - current_kijun) / current_kijun
            strength = min(0.7 + separation * 10, 1.0)
            return 'bearish_tk', strength
        
        return 'none', 0.0
    
    def check_chikou_span_confirmation(self, data: pd.DataFrame, signal_type: str) -> float:
        """
        Check Chikou Span confirmation for signals.
        
        Args:
            data: DataFrame with Ichimoku components
            signal_type: 'bullish' or 'bearish'
            
        Returns:
            Confirmation strength (0.0 to 1.0)
        """
        if len(data) < self.displacement + 1:
            return 0.5  # Neutral if insufficient data
        
        # Chikou Span is current price shifted back by displacement periods
        current_chikou = data['Chikou_Span'].iloc[-1]
        
        # Compare with price at the same time period
        if len(data) >= self.displacement:
            comparison_price = data['close'].iloc[-(self.displacement + 1)]
            
            if pd.isna(current_chikou) or pd.isna(comparison_price):
                return 0.5
            
            if signal_type == 'bullish':
                # For bullish signals, prefer Chikou above historical price
                if current_chikou > comparison_price:
                    strength = (current_chikou - comparison_price) / comparison_price
                    return min(strength * 5 + 0.6, 1.0)
                else:
                    return 0.3
            
            elif signal_type == 'bearish':
                # For bearish signals, prefer Chikou below historical price
                if current_chikou < comparison_price:
                    strength = (comparison_price - current_chikou) / comparison_price
                    return min(strength * 5 + 0.6, 1.0)
                else:
                    return 0.3
        
        return 0.5
    
    def calculate_cloud_strength(self, data: pd.DataFrame) -> float:
        """
        Calculate cloud strength based on thickness and consistency.
        
        Args:
            data: DataFrame with Ichimoku components
            
        Returns:
            Cloud strength (0.0 to 1.0)
        """
        if len(data) < 10:
            return 0.5
        
        # Look at recent cloud data
        recent_data = data.tail(10)
        
        # Calculate average cloud thickness
        avg_thickness = recent_data['Cloud_Thickness'].mean()
        current_price = data['close'].iloc[-1]
        
        if pd.isna(avg_thickness) or current_price == 0:
            return 0.5
        
        # Normalize thickness relative to price
        thickness_ratio = avg_thickness / current_price
        
        # Check cloud consistency (same color)
        span_a_above_b = (recent_data['Senkou_Span_A'] > recent_data['Senkou_Span_B']).sum()
        consistency = abs(span_a_above_b - 5) / 5  # 0 = most consistent, 1 = least consistent
        
        # Combine thickness and consistency
        strength = min(thickness_ratio * 20, 0.7) + (1 - consistency) * 0.3
        
        return min(strength, 1.0)
    
    def analyze(self, data: pd.DataFrame) -> Signal:
        """
        Analyze market data and generate trading signal.
        
        Args:
            data: OHLCV DataFrame
            
        Returns:
            Signal object with trading recommendation
        """
        if not self.validate_data(data):
            return Signal(
                action='HOLD',
                strength=0.0,
                price=data['close'].iloc[-1] if len(data) > 0 else 0.0,
                timestamp=datetime.now(),
                reason="Insufficient or invalid data"
            )
        
        # Calculate Ichimoku components
        df_with_ichimoku = self.calculate_ichimoku_components(data)
        
        # Check if we have enough data after calculation
        required_cols = ['Tenkan_Sen', 'Kijun_Sen', 'Senkou_Span_A', 'Senkou_Span_B']
        if any(df_with_ichimoku[col].isna().all() for col in required_cols):
            return Signal(
                action='HOLD',
                strength=0.0,
                price=data['close'].iloc[-1],
                timestamp=datetime.now(),
                reason="Insufficient data for Ichimoku calculation"
            )
        
        # Analyze components
        cloud_position, cloud_position_strength = self.analyze_price_cloud_position(df_with_ichimoku)
        tk_crossover, tk_strength = self.detect_tenkan_kijun_crossover(df_with_ichimoku)
        cloud_strength = self.calculate_cloud_strength(df_with_ichimoku)
        
        current_price = data['close'].iloc[-1]
        current_tenkan = df_with_ichimoku['Tenkan_Sen'].iloc[-1]
        current_kijun = df_with_ichimoku['Kijun_Sen'].iloc[-1]
        
        # Generate signals based on analysis
        if (tk_crossover == 'bullish_tk' and 
            cloud_position in ['above_cloud', 'in_cloud']):
            
            chikou_confirmation = self.check_chikou_span_confirmation(df_with_ichimoku, 'bullish')
            
            # Combine all factors for final signal strength
            final_strength = (
                tk_strength * 0.4 +
                cloud_position_strength * 0.3 +
                chikou_confirmation * 0.2 +
                cloud_strength * 0.1
            )
            
            if final_strength >= self.min_signal_strength:
                signal = Signal(
                    action='BUY',
                    strength=final_strength,
                    price=current_price,
                    timestamp=datetime.now(),
                    reason=f"Bullish Ichimoku signal. Tenkan-Kijun crossover with price {cloud_position}",
                    metadata={
                        'tenkan_sen': current_tenkan,
                        'kijun_sen': current_kijun,
                        'cloud_position': cloud_position,
                        'tk_crossover': tk_crossover,
                        'tk_strength': tk_strength,
                        'cloud_position_strength': cloud_position_strength,
                        'chikou_confirmation': chikou_confirmation,
                        'cloud_strength': cloud_strength
                    }
                )
            else:
                signal = Signal(
                    action='HOLD',
                    strength=final_strength,
                    price=current_price,
                    timestamp=datetime.now(),
                    reason=f"Bullish Ichimoku setup but insufficient strength: {final_strength:.2f}",
                    metadata={'final_strength': final_strength}
                )
        
        elif (tk_crossover == 'bearish_tk' and 
              cloud_position in ['below_cloud', 'in_cloud']):
            
            chikou_confirmation = self.check_chikou_span_confirmation(df_with_ichimoku, 'bearish')
            
            # Combine all factors for final signal strength
            final_strength = (
                tk_strength * 0.4 +
                cloud_position_strength * 0.3 +
                chikou_confirmation * 0.2 +
                cloud_strength * 0.1
            )
            
            if final_strength >= self.min_signal_strength:
                signal = Signal(
                    action='SELL',
                    strength=final_strength,
                    price=current_price,
                    timestamp=datetime.now(),
                    reason=f"Bearish Ichimoku signal. Tenkan-Kijun crossover with price {cloud_position}",
                    metadata={
                        'tenkan_sen': current_tenkan,
                        'kijun_sen': current_kijun,
                        'cloud_position': cloud_position,
                        'tk_crossover': tk_crossover,
                        'tk_strength': tk_strength,
                        'cloud_position_strength': cloud_position_strength,
                        'chikou_confirmation': chikou_confirmation,
                        'cloud_strength': cloud_strength
                    }
                )
            else:
                signal = Signal(
                    action='HOLD',
                    strength=final_strength,
                    price=current_price,
                    timestamp=datetime.now(),
                    reason=f"Bearish Ichimoku setup but insufficient strength: {final_strength:.2f}",
                    metadata={'final_strength': final_strength}
                )
        
        else:
            # No significant Ichimoku signal
            signal = Signal(
                action='HOLD',
                strength=0.0,
                price=current_price,
                timestamp=datetime.now(),
                reason=f"No significant Ichimoku signal. TK: {tk_crossover}, Cloud: {cloud_position}",
                metadata={
                    'tenkan_sen': current_tenkan,
                    'kijun_sen': current_kijun,
                    'cloud_position': cloud_position,
                    'tk_crossover': tk_crossover,
                    'cloud_strength': cloud_strength
                }
            )
        
        # Add signal to history
        self.signals_history.append(signal)
        
        # Keep only last 100 signals in history
        if len(self.signals_history) > 100:
            self.signals_history = self.signals_history[-100:]
        
        return signal
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """Get strategy information and current parameters."""
        return {
            'name': self.name,
            'tenkan_period': self.tenkan_period,
            'kijun_period': self.kijun_period,
            'senkou_span_b_period': self.senkou_span_b_period,
            'displacement': self.displacement,
            'min_signal_strength': self.min_signal_strength,
            'active_positions': len(self.positions),
            'total_signals': len(self.signals_history),
            'performance': self.get_performance_metrics()
        }
