"""
Moving Average Crossover Strategy.
Generates buy/sell signals based on short and long moving average crossovers.
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import List, Dict, Any

from .base_strategy import BaseStrategy, Signal


class MovingAverageCrossoverStrategy(BaseStrategy):
    """
    Moving Average Crossover trading strategy.
    
    Generates BUY signal when short MA crosses above long MA.
    Generates SELL signal when short MA crosses below long MA.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize MA Crossover strategy.
        
        Args:
            config: Strategy configuration with parameters:
                - short_period: Short MA period (default: 10)
                - long_period: Long MA period (default: 30)
                - ma_type: MA type ('SMA' or 'EMA', default: 'SMA')
                - min_crossover_strength: Minimum strength for signal (default: 0.6)
        """
        default_config = {
            'short_period': 10,
            'long_period': 30,
            'ma_type': 'SMA',
            'min_crossover_strength': 0.6,
            'max_position_size': 0.1,
            'stop_loss_percentage': 0.02,
            'take_profit_percentage': 0.05
        }
        
        if config:
            default_config.update(config)
        
        super().__init__("MA Crossover", default_config)
        
        self.short_period = self.config['short_period']
        self.long_period = self.config['long_period']
        self.ma_type = self.config['ma_type']
        self.min_crossover_strength = self.config['min_crossover_strength']
        
        # Validate configuration
        if self.short_period >= self.long_period:
            raise ValueError("Short period must be less than long period")
    
    def get_required_indicators(self) -> List[str]:
        """Get required indicators for this strategy."""
        return [f'{self.ma_type}_{self.short_period}', f'{self.ma_type}_{self.long_period}']
    
    def get_minimum_data_length(self) -> int:
        """Get minimum data length required."""
        return max(self.long_period * 2, 50)
    
    def calculate_moving_averages(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate moving averages for the strategy.
        
        Args:
            data: OHLCV DataFrame
            
        Returns:
            DataFrame with MA columns added
        """
        df = data.copy()
        
        if self.ma_type == 'SMA':
            df[f'MA_short'] = df['close'].rolling(window=self.short_period).mean()
            df[f'MA_long'] = df['close'].rolling(window=self.long_period).mean()
        elif self.ma_type == 'EMA':
            df[f'MA_short'] = df['close'].ewm(span=self.short_period).mean()
            df[f'MA_long'] = df['close'].ewm(span=self.long_period).mean()
        else:
            raise ValueError(f"Unsupported MA type: {self.ma_type}")
        
        return df
    
    def detect_crossover(self, data: pd.DataFrame) -> tuple:
        """
        Detect crossover events in the data.
        
        Args:
            data: DataFrame with MA columns
            
        Returns:
            Tuple of (crossover_type, strength) where:
            - crossover_type: 'bullish', 'bearish', or 'none'
            - strength: Signal strength (0.0 to 1.0)
        """
        if len(data) < 2:
            return 'none', 0.0
        
        # Get current and previous MA values
        current_short = data['MA_short'].iloc[-1]
        current_long = data['MA_long'].iloc[-1]
        prev_short = data['MA_short'].iloc[-2]
        prev_long = data['MA_long'].iloc[-2]
        
        # Check for crossovers
        bullish_crossover = (prev_short <= prev_long) and (current_short > current_long)
        bearish_crossover = (prev_short >= prev_long) and (current_short < current_long)
        
        if bullish_crossover:
            # Calculate strength based on MA separation and momentum
            separation = abs(current_short - current_long) / current_long
            momentum = self._calculate_momentum(data)
            strength = min(0.5 + separation * 10 + momentum * 0.3, 1.0)
            return 'bullish', strength
        
        elif bearish_crossover:
            # Calculate strength based on MA separation and momentum
            separation = abs(current_short - current_long) / current_long
            momentum = self._calculate_momentum(data)
            strength = min(0.5 + separation * 10 + abs(momentum) * 0.3, 1.0)
            return 'bearish', strength
        
        return 'none', 0.0
    
    def _calculate_momentum(self, data: pd.DataFrame) -> float:
        """
        Calculate price momentum to enhance signal strength.
        
        Args:
            data: OHLCV DataFrame
            
        Returns:
            Momentum value (-1.0 to 1.0)
        """
        if len(data) < 5:
            return 0.0
        
        # Calculate 5-period price change
        current_price = data['close'].iloc[-1]
        past_price = data['close'].iloc[-5]
        
        momentum = (current_price - past_price) / past_price
        
        # Normalize to -1.0 to 1.0 range
        return max(-1.0, min(1.0, momentum * 10))
    
    def _calculate_trend_strength(self, data: pd.DataFrame) -> float:
        """
        Calculate overall trend strength.
        
        Args:
            data: DataFrame with MA columns
            
        Returns:
            Trend strength (0.0 to 1.0)
        """
        if len(data) < self.long_period:
            return 0.0
        
        # Calculate MA slope over recent periods
        recent_data = data.tail(10)
        
        short_slope = (recent_data['MA_short'].iloc[-1] - recent_data['MA_short'].iloc[0]) / len(recent_data)
        long_slope = (recent_data['MA_long'].iloc[-1] - recent_data['MA_long'].iloc[0]) / len(recent_data)
        
        # Normalize slopes
        price_range = data['close'].tail(20).max() - data['close'].tail(20).min()
        if price_range == 0:
            return 0.0
        
        normalized_short_slope = abs(short_slope) / price_range
        normalized_long_slope = abs(long_slope) / price_range
        
        # Combine slopes for trend strength
        trend_strength = (normalized_short_slope + normalized_long_slope) / 2
        
        return min(trend_strength * 100, 1.0)
    
    def analyze(self, data: pd.DataFrame) -> Signal:
        """
        Analyze market data and generate trading signal.
        
        Args:
            data: OHLCV DataFrame
            
        Returns:
            Signal object with trading recommendation
        """
        if not self.validate_data(data):
            return Signal(
                action='HOLD',
                strength=0.0,
                price=data['close'].iloc[-1] if len(data) > 0 else 0.0,
                timestamp=datetime.now(),
                reason="Insufficient or invalid data"
            )
        
        # Calculate moving averages
        df_with_ma = self.calculate_moving_averages(data)
        
        # Check if we have enough data after MA calculation
        if df_with_ma['MA_long'].isna().any():
            return Signal(
                action='HOLD',
                strength=0.0,
                price=data['close'].iloc[-1],
                timestamp=datetime.now(),
                reason="Insufficient data for MA calculation"
            )
        
        # Detect crossover
        crossover_type, crossover_strength = self.detect_crossover(df_with_ma)
        
        current_price = data['close'].iloc[-1]
        current_short_ma = df_with_ma['MA_short'].iloc[-1]
        current_long_ma = df_with_ma['MA_long'].iloc[-1]
        
        # Generate signal based on crossover
        if crossover_type == 'bullish' and crossover_strength >= self.min_crossover_strength:
            trend_strength = self._calculate_trend_strength(df_with_ma)
            final_strength = (crossover_strength + trend_strength) / 2
            
            signal = Signal(
                action='BUY',
                strength=final_strength,
                price=current_price,
                timestamp=datetime.now(),
                reason=f"Bullish MA crossover detected. Short MA ({current_short_ma:.2f}) > Long MA ({current_long_ma:.2f})",
                metadata={
                    'short_ma': current_short_ma,
                    'long_ma': current_long_ma,
                    'crossover_strength': crossover_strength,
                    'trend_strength': trend_strength
                }
            )
        
        elif crossover_type == 'bearish' and crossover_strength >= self.min_crossover_strength:
            trend_strength = self._calculate_trend_strength(df_with_ma)
            final_strength = (crossover_strength + trend_strength) / 2
            
            signal = Signal(
                action='SELL',
                strength=final_strength,
                price=current_price,
                timestamp=datetime.now(),
                reason=f"Bearish MA crossover detected. Short MA ({current_short_ma:.2f}) < Long MA ({current_long_ma:.2f})",
                metadata={
                    'short_ma': current_short_ma,
                    'long_ma': current_long_ma,
                    'crossover_strength': crossover_strength,
                    'trend_strength': trend_strength
                }
            )
        
        else:
            # No significant crossover or below threshold
            signal = Signal(
                action='HOLD',
                strength=0.0,
                price=current_price,
                timestamp=datetime.now(),
                reason=f"No significant crossover. Short MA: {current_short_ma:.2f}, Long MA: {current_long_ma:.2f}",
                metadata={
                    'short_ma': current_short_ma,
                    'long_ma': current_long_ma,
                    'crossover_type': crossover_type,
                    'crossover_strength': crossover_strength
                }
            )
        
        # Add signal to history
        self.signals_history.append(signal)
        
        # Keep only last 100 signals in history
        if len(self.signals_history) > 100:
            self.signals_history = self.signals_history[-100:]
        
        return signal
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """Get strategy information and current parameters."""
        return {
            'name': self.name,
            'short_period': self.short_period,
            'long_period': self.long_period,
            'ma_type': self.ma_type,
            'min_crossover_strength': self.min_crossover_strength,
            'active_positions': len(self.positions),
            'total_signals': len(self.signals_history),
            'performance': self.get_performance_metrics()
        }
