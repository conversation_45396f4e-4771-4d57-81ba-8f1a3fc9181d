# AI Trading Bot

A sophisticated cryptocurrency trading bot built with Python and Kivy, featuring multiple trading strategies, exchange integrations, and both simple and advanced UI options.

## Features

### Core Trading Features
- **Multiple Exchange Support**: Binance and Indodax integration
- **Advanced Trading Strategies**: 
  - Moving Average Crossover
  - RSI Strategy
  - Bollinger Bands
  - Ichimoku Cloud
- **Risk Management**: Stop-loss, take-profit, position sizing
- **Real-time Market Data**: Live price feeds and historical data
- **Performance Analytics**: Trade recording and metrics visualization
- **Notification System**: Telegram and email alerts

### User Interface Options
- **Simple UI**: Standard Kivy interface for lightweight usage
- **Cyberfunk UI**: KivyMD with futuristic cyberpunk theme
- **Mobile Ready**: Buildozer configuration for Android deployment

### Technical Features
- **Backtesting**: Test strategies on historical data
- **Paper Trading**: Simulate trading without real money
- **Technical Indicators**: Comprehensive TA-Lib integration
- **Database Storage**: SQLite for trade history and configuration
- **Comprehensive Logging**: Detailed logging system

## Installation

### Prerequisites
- Python 3.8 or higher
- pip package manager

### Install Dependencies
```bash
pip install -r requirements.txt
```

### Additional Requirements for TA-Lib
On Windows:
```bash
# Download TA-Lib wheel from https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib
pip install TA_Lib-0.4.28-cp39-cp39-win_amd64.whl
```

On Linux/macOS:
```bash
# Install TA-Lib system dependency first
sudo apt-get install libta-lib-dev  # Ubuntu/Debian
brew install ta-lib  # macOS

pip install TA-Lib
```

## Quick Start

### 1. Basic Configuration
Edit `config/trading_config.json` to set up your exchange API credentials:

```json
{
  "exchanges": {
    "binance": {
      "enabled": true,
      "testnet": true,
      "api_key": "your_api_key_here",
      "api_secret": "your_api_secret_here"
    }
  }
}
```

### 2. Run the Application

**Simple Kivy Interface:**
```bash
python main.py
```

**Cyberfunk KivyMD Interface:**
```bash
python cyberfunk_trading_app.py
```

### 3. First Steps
1. Configure your exchange API credentials in the Settings tab
2. Select a trading strategy
3. Set risk management parameters
4. Start with paper trading to test your setup
5. Monitor performance and adjust as needed

## Project Structure

```
├── main.py                     # Simple Kivy UI entry point
├── cyberfunk_trading_app.py    # KivyMD Cyberfunk UI entry point
├── cyberfunk_theme.py          # Cyberfunk theme definition
├── requirements.txt            # Python dependencies
├── bot/                        # Core trading logic
│   ├── config_loader.py        # Configuration management
│   ├── logger.py               # Logging utilities
│   ├── market_data.py          # Market data simulation
│   ├── exchanges/              # Exchange integrations
│   ├── strategies/             # Trading strategies
│   ├── indicators.py           # Technical indicators
│   ├── performance/            # Performance analysis
│   └── notifications/          # Notification system
├── ui/                         # User interface components
├── config/                     # Configuration files
├── data/                       # Application data
└── logs/                       # Application logs
```

## Configuration

### Trading Configuration
The main configuration file is `config/trading_config.json`:

- **Trading Settings**: Default strategy, risk management, trading pairs
- **Exchange Settings**: API credentials, testnet mode
- **Strategy Parameters**: Specific settings for each strategy
- **Notifications**: Telegram and email notification settings
- **UI Settings**: Theme, refresh intervals, chart indicators

### Environment Variables
For security, you can also use environment variables:
```bash
export BINANCE_API_KEY="your_api_key"
export BINANCE_API_SECRET="your_api_secret"
export TELEGRAM_BOT_TOKEN="your_bot_token"
export TELEGRAM_CHAT_ID="your_chat_id"
```

## Trading Strategies

### 1. Moving Average Crossover
- **Fast MA**: Short-term moving average (default: 10 periods)
- **Slow MA**: Long-term moving average (default: 30 periods)
- **Signal**: Buy when fast MA crosses above slow MA, sell when below

### 2. RSI Strategy
- **Period**: RSI calculation period (default: 14)
- **Oversold**: Buy signal threshold (default: 30)
- **Overbought**: Sell signal threshold (default: 70)

### 3. Bollinger Bands
- **Period**: Moving average period (default: 20)
- **Standard Deviation**: Band width (default: 2)
- **Signal**: Buy at lower band, sell at upper band

### 4. Ichimoku Cloud
- **Tenkan**: Fast line period (default: 9)
- **Kijun**: Medium line period (default: 26)
- **Senkou Span B**: Slow line period (default: 52)

## Risk Management

### Position Sizing
- **Max Position Size**: Maximum percentage of portfolio per trade
- **Dynamic Sizing**: Adjust position size based on volatility

### Stop Loss & Take Profit
- **Stop Loss**: Automatic loss limitation
- **Take Profit**: Automatic profit taking
- **Trailing Stops**: Dynamic stop loss adjustment

## Mobile Deployment

### Android Build
```bash
# Install buildozer
pip install buildozer

# Initialize buildozer (first time only)
buildozer init

# Build APK
buildozer android debug

# Build and deploy to connected device
buildozer android deploy run
```

### iOS Build (macOS only)
```bash
# Install kivy-ios
pip install kivy-ios

# Build iOS app
toolchain build python3 kivy
toolchain create <YourApp> <path_to_your_app_directory>
```

## Development

### Adding New Strategies
1. Create a new strategy class in `bot/strategies/`
2. Inherit from `BaseStrategy`
3. Implement required methods: `generate_signal()`, `get_parameters()`
4. Add strategy to configuration file

### Adding New Exchanges
1. Create a new exchange class in `bot/exchanges/`
2. Inherit from `BaseExchange`
3. Implement all abstract methods
4. Add exchange configuration

### Custom Indicators
1. Add indicator functions to `bot/indicators.py`
2. Use TA-Lib or implement custom calculations
3. Integrate with strategies as needed

## Troubleshooting

### Common Issues

**TA-Lib Installation Error:**
- Ensure system dependencies are installed
- Use pre-compiled wheels for Windows
- Check Python version compatibility

**Exchange Connection Failed:**
- Verify API credentials
- Check network connectivity
- Ensure testnet mode matches your API keys

**Kivy/KivyMD Issues:**
- Update to latest versions
- Check Python version compatibility
- Install system dependencies (SDL2, etc.)

### Logging
Check `logs/trading_bot.log` for detailed error messages and debugging information.

## Security

### API Key Security
- Never commit API keys to version control
- Use environment variables for production
- Enable IP restrictions on exchange APIs
- Use testnet for development

### Best Practices
- Start with small amounts
- Test thoroughly in paper trading mode
- Monitor bot performance regularly
- Keep software updated

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This software is for educational purposes only. Cryptocurrency trading involves significant risk of loss. The authors are not responsible for any financial losses incurred while using this software. Always do your own research and never invest more than you can afford to lose.

## Support

For support, please:
1. Check the troubleshooting section
2. Review the logs for error messages
3. Open an issue on GitHub
4. Join our community discussions

---

**Happy Trading! 🚀**