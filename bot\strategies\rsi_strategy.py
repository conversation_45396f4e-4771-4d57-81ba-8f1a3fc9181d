"""
RSI (Relative Strength Index) Strategy.
Generates buy/sell signals based on RSI overbought and oversold conditions.
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import List, Dict, Any

from .base_strategy import BaseStrategy, Signal


class RSIStrategy(BaseStrategy):
    """
    RSI-based trading strategy.
    
    Generates BUY signal when RSI is oversold and starts to recover.
    Generates SELL signal when RSI is overbought and starts to decline.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize RSI strategy.
        
        Args:
            config: Strategy configuration with parameters:
                - rsi_period: RSI calculation period (default: 14)
                - oversold_threshold: Oversold level (default: 30)
                - overbought_threshold: Overbought level (default: 70)
                - extreme_oversold: Extreme oversold level (default: 20)
                - extreme_overbought: Extreme overbought level (default: 80)
                - min_signal_strength: Minimum strength for signal (default: 0.5)
        """
        default_config = {
            'rsi_period': 14,
            'oversold_threshold': 30,
            'overbought_threshold': 70,
            'extreme_oversold': 20,
            'extreme_overbought': 80,
            'min_signal_strength': 0.5,
            'max_position_size': 0.1,
            'stop_loss_percentage': 0.03,
            'take_profit_percentage': 0.06
        }
        
        if config:
            default_config.update(config)
        
        super().__init__("RSI Strategy", default_config)
        
        self.rsi_period = self.config['rsi_period']
        self.oversold_threshold = self.config['oversold_threshold']
        self.overbought_threshold = self.config['overbought_threshold']
        self.extreme_oversold = self.config['extreme_oversold']
        self.extreme_overbought = self.config['extreme_overbought']
        self.min_signal_strength = self.config['min_signal_strength']
    
    def get_required_indicators(self) -> List[str]:
        """Get required indicators for this strategy."""
        return [f'RSI_{self.rsi_period}']
    
    def get_minimum_data_length(self) -> int:
        """Get minimum data length required."""
        return max(self.rsi_period * 3, 50)
    
    def calculate_rsi(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate RSI indicator.
        
        Args:
            data: OHLCV DataFrame
            
        Returns:
            DataFrame with RSI column added
        """
        df = data.copy()
        
        # Calculate price changes
        delta = df['close'].diff()
        
        # Separate gains and losses
        gains = delta.where(delta > 0, 0)
        losses = -delta.where(delta < 0, 0)
        
        # Calculate average gains and losses
        avg_gains = gains.rolling(window=self.rsi_period).mean()
        avg_losses = losses.rolling(window=self.rsi_period).mean()
        
        # Calculate RS and RSI
        rs = avg_gains / avg_losses
        rsi = 100 - (100 / (1 + rs))
        
        df['RSI'] = rsi
        
        return df
    
    def detect_rsi_signals(self, data: pd.DataFrame) -> tuple:
        """
        Detect RSI-based trading signals.
        
        Args:
            data: DataFrame with RSI column
            
        Returns:
            Tuple of (signal_type, strength) where:
            - signal_type: 'buy', 'sell', or 'hold'
            - strength: Signal strength (0.0 to 1.0)
        """
        if len(data) < 3:
            return 'hold', 0.0
        
        current_rsi = data['RSI'].iloc[-1]
        prev_rsi = data['RSI'].iloc[-2]
        prev2_rsi = data['RSI'].iloc[-3]
        
        # Check for oversold recovery (buy signal)
        if (current_rsi <= self.oversold_threshold and 
            current_rsi > prev_rsi and 
            prev_rsi <= prev2_rsi):
            
            # Calculate signal strength based on RSI level and momentum
            if current_rsi <= self.extreme_oversold:
                base_strength = 0.9
            else:
                # Linear interpolation between extreme and normal oversold
                base_strength = 0.5 + (self.oversold_threshold - current_rsi) / (self.oversold_threshold - self.extreme_oversold) * 0.4
            
            # Add momentum component
            momentum = (current_rsi - prev_rsi) / 10  # Normalize momentum
            strength = min(base_strength + momentum, 1.0)
            
            return 'buy', strength
        
        # Check for overbought decline (sell signal)
        elif (current_rsi >= self.overbought_threshold and 
              current_rsi < prev_rsi and 
              prev_rsi >= prev2_rsi):
            
            # Calculate signal strength based on RSI level and momentum
            if current_rsi >= self.extreme_overbought:
                base_strength = 0.9
            else:
                # Linear interpolation between normal and extreme overbought
                base_strength = 0.5 + (current_rsi - self.overbought_threshold) / (self.extreme_overbought - self.overbought_threshold) * 0.4
            
            # Add momentum component
            momentum = abs(current_rsi - prev_rsi) / 10  # Normalize momentum
            strength = min(base_strength + momentum, 1.0)
            
            return 'sell', strength
        
        return 'hold', 0.0
    
    def calculate_rsi_divergence(self, data: pd.DataFrame) -> float:
        """
        Calculate RSI divergence with price for additional signal confirmation.
        
        Args:
            data: DataFrame with price and RSI data
            
        Returns:
            Divergence strength (0.0 to 1.0)
        """
        if len(data) < 10:
            return 0.0
        
        # Look at last 10 periods for divergence
        recent_data = data.tail(10)
        
        # Calculate price and RSI trends
        price_trend = (recent_data['close'].iloc[-1] - recent_data['close'].iloc[0]) / recent_data['close'].iloc[0]
        rsi_trend = (recent_data['RSI'].iloc[-1] - recent_data['RSI'].iloc[0]) / 100
        
        # Bullish divergence: price declining but RSI rising
        if price_trend < 0 and rsi_trend > 0:
            divergence_strength = min(abs(price_trend) + abs(rsi_trend), 1.0)
            return divergence_strength
        
        # Bearish divergence: price rising but RSI declining
        elif price_trend > 0 and rsi_trend < 0:
            divergence_strength = min(abs(price_trend) + abs(rsi_trend), 1.0)
            return divergence_strength
        
        return 0.0
    
    def calculate_volume_confirmation(self, data: pd.DataFrame) -> float:
        """
        Calculate volume confirmation for RSI signals.
        
        Args:
            data: DataFrame with volume data
            
        Returns:
            Volume confirmation strength (0.0 to 1.0)
        """
        if len(data) < 5:
            return 0.5  # Neutral if insufficient data
        
        # Compare current volume to average volume
        current_volume = data['volume'].iloc[-1]
        avg_volume = data['volume'].tail(5).mean()
        
        if avg_volume == 0:
            return 0.5
        
        volume_ratio = current_volume / avg_volume
        
        # Higher volume increases signal confidence
        if volume_ratio > 1.5:
            return 1.0
        elif volume_ratio > 1.2:
            return 0.8
        elif volume_ratio > 0.8:
            return 0.6
        else:
            return 0.4
    
    def analyze(self, data: pd.DataFrame) -> Signal:
        """
        Analyze market data and generate trading signal.
        
        Args:
            data: OHLCV DataFrame
            
        Returns:
            Signal object with trading recommendation
        """
        if not self.validate_data(data):
            return Signal(
                action='HOLD',
                strength=0.0,
                price=data['close'].iloc[-1] if len(data) > 0 else 0.0,
                timestamp=datetime.now(),
                reason="Insufficient or invalid data"
            )
        
        # Calculate RSI
        df_with_rsi = self.calculate_rsi(data)
        
        # Check if we have enough data after RSI calculation
        if df_with_rsi['RSI'].isna().any():
            return Signal(
                action='HOLD',
                strength=0.0,
                price=data['close'].iloc[-1],
                timestamp=datetime.now(),
                reason="Insufficient data for RSI calculation"
            )
        
        # Detect RSI signals
        signal_type, base_strength = self.detect_rsi_signals(df_with_rsi)
        
        current_price = data['close'].iloc[-1]
        current_rsi = df_with_rsi['RSI'].iloc[-1]
        
        # Calculate additional confirmations
        divergence_strength = self.calculate_rsi_divergence(df_with_rsi)
        volume_confirmation = self.calculate_volume_confirmation(data)
        
        # Combine all factors for final signal strength
        if signal_type in ['buy', 'sell']:
            final_strength = (base_strength * 0.6 + 
                            divergence_strength * 0.2 + 
                            volume_confirmation * 0.2)
        else:
            final_strength = 0.0
        
        # Generate signal based on RSI analysis
        if signal_type == 'buy' and final_strength >= self.min_signal_strength:
            signal = Signal(
                action='BUY',
                strength=final_strength,
                price=current_price,
                timestamp=datetime.now(),
                reason=f"RSI oversold recovery signal. RSI: {current_rsi:.2f}",
                metadata={
                    'rsi': current_rsi,
                    'base_strength': base_strength,
                    'divergence_strength': divergence_strength,
                    'volume_confirmation': volume_confirmation,
                    'oversold_threshold': self.oversold_threshold
                }
            )
        
        elif signal_type == 'sell' and final_strength >= self.min_signal_strength:
            signal = Signal(
                action='SELL',
                strength=final_strength,
                price=current_price,
                timestamp=datetime.now(),
                reason=f"RSI overbought decline signal. RSI: {current_rsi:.2f}",
                metadata={
                    'rsi': current_rsi,
                    'base_strength': base_strength,
                    'divergence_strength': divergence_strength,
                    'volume_confirmation': volume_confirmation,
                    'overbought_threshold': self.overbought_threshold
                }
            )
        
        else:
            # No significant signal or below threshold
            signal = Signal(
                action='HOLD',
                strength=0.0,
                price=current_price,
                timestamp=datetime.now(),
                reason=f"No significant RSI signal. RSI: {current_rsi:.2f}",
                metadata={
                    'rsi': current_rsi,
                    'signal_type': signal_type,
                    'base_strength': base_strength,
                    'final_strength': final_strength
                }
            )
        
        # Add signal to history
        self.signals_history.append(signal)
        
        # Keep only last 100 signals in history
        if len(self.signals_history) > 100:
            self.signals_history = self.signals_history[-100:]
        
        return signal
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """Get strategy information and current parameters."""
        return {
            'name': self.name,
            'rsi_period': self.rsi_period,
            'oversold_threshold': self.oversold_threshold,
            'overbought_threshold': self.overbought_threshold,
            'extreme_oversold': self.extreme_oversold,
            'extreme_overbought': self.extreme_overbought,
            'min_signal_strength': self.min_signal_strength,
            'active_positions': len(self.positions),
            'total_signals': len(self.signals_history),
            'performance': self.get_performance_metrics()
        }
