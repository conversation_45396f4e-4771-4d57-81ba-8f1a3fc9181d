from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
import pandas as pd
from datetime import datetime

class BaseExchange(ABC):
    """Abstract base class for exchange implementations."""
    
    def __init__(self, api_key: str = "", api_secret: str = "", testnet: bool = True):
        self.api_key = api_key
        self.api_secret = api_secret
        self.testnet = testnet
        self.connected = False
    
    @abstractmethod
    def connect(self) -> bool:
        """Connect to the exchange."""
        pass
    
    @abstractmethod
    def disconnect(self):
        """Disconnect from the exchange."""
        pass
    
    @abstractmethod
    def get_account_balance(self) -> Dict[str, float]:
        """Get account balance for all assets."""
        pass
    
    @abstractmethod
    def get_current_price(self, symbol: str) -> float:
        """Get current price for a trading pair."""
        pass
    
    @abstractmethod
    def get_historical_data(self, symbol: str, timeframe: str, limit: int) -> pd.DataFrame:
        """Get historical OHLCV data."""
        pass
    
    @abstractmethod
    def place_market_order(self, symbol: str, side: str, quantity: float) -> Dict[str, Any]:
        """Place a market order."""
        pass
    
    @abstractmethod
    def place_limit_order(self, symbol: str, side: str, quantity: float, price: float) -> Dict[str, Any]:
        """Place a limit order."""
        pass
    
    @abstractmethod
    def cancel_order(self, symbol: str, order_id: str) -> bool:
        """Cancel an order."""
        pass
    
    @abstractmethod
    def get_open_orders(self, symbol: str = None) -> List[Dict[str, Any]]:
        """Get open orders."""
        pass
    
    @abstractmethod
    def get_order_status(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """Get order status."""
        pass
    
    @abstractmethod
    def get_trade_history(self, symbol: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get trade history."""
        pass
    
    @abstractmethod
    def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """Get ticker information."""
        pass
    
    @abstractmethod
    def get_order_book(self, symbol: str, limit: int = 100) -> Dict[str, Any]:
        """Get order book."""
        pass
    
    @abstractmethod
    def get_available_symbols(self) -> List[str]:
        """Get available trading symbols."""
        pass
    
    def validate_symbol(self, symbol: str) -> bool:
        """Validate if symbol is available for trading."""
        available_symbols = self.get_available_symbols()
        return symbol in available_symbols
    
    def validate_order_params(self, symbol: str, side: str, quantity: float, price: float = None) -> bool:
        """Validate order parameters."""
        if not self.validate_symbol(symbol):
            return False
        
        if side not in ['buy', 'sell']:
            return False
        
        if quantity <= 0:
            return False
        
        if price is not None and price <= 0:
            return False
        
        return True
    
    def get_exchange_info(self) -> Dict[str, Any]:
        """Get exchange information."""
        return {
            'name': self.__class__.__name__,
            'connected': self.connected,
            'testnet': self.testnet,
            'available_symbols': self.get_available_symbols()
        }