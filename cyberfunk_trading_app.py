"""
KivyMD entry point for the Cyberfunk AI Trading Bot application.
Features a futuristic cyberpunk-themed interface with advanced UI components.
"""

import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import <PERSON><PERSON>abe<PERSON>
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.navigationdrawer import MDNavigationDrawer, MDNavigationDrawerMenu
from kivymd.uix.screenmanager import MDScreenManager
from kivymd.uix.card import MDCard
from kivymd.uix.bottomnavigation import MDBottomNavigation, MDBottomNavigationItem
from kivy.clock import Clock
from kivy.logger import Logger

from cyberfunk_theme import CyberfunkTheme, CYBERFUNK_STYLES
from bot.config_loader import ConfigLoader
from bot.logger import trading_logger
from ui.kivymd_main_screen import KivyMDMainScreen


class CyberfunkTradingApp(MDApp):
    """Main KivyMD application class for the cyberfunk trading bot."""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "Cyberfunk AI Trading Bot"
        self.config_loader = None
        self.logger = trading_logger.get_logger('CyberfunkApp')
        
    def build(self):
        """Build the main application interface with cyberfunk theme."""
        try:
            # Apply cyberfunk theme
            self._apply_cyberfunk_theme()
            
            # Load configuration
            self.config_loader = ConfigLoader()
            
            # Create main screen manager
            screen_manager = MDScreenManager()
            
            # Create main screen
            main_screen = KivyMDMainScreen(
                config_loader=self.config_loader,
                name="main"
            )
            screen_manager.add_widget(main_screen)
            
            self.logger.info("Cyberfunk Trading Bot application started")
            return screen_manager
            
        except Exception as e:
            self.logger.error(f"Failed to build application: {e}")
            return self._create_error_screen(str(e))
    
    def _apply_cyberfunk_theme(self):
        """Apply the cyberfunk theme to the application."""
        theme_config = CyberfunkTheme.get_theme_config()
        
        self.theme_cls.theme_style = theme_config["theme_style"]
        self.theme_cls.primary_palette = theme_config["primary_palette"]
        self.theme_cls.accent_palette = theme_config["accent_palette"]
        self.theme_cls.primary_hue = theme_config["primary_hue"]
        self.theme_cls.accent_hue = theme_config["accent_hue"]
        
        # Set custom colors
        color_scheme = CyberfunkTheme.get_color_scheme()
        self.theme_cls.bg_dark = color_scheme["background_dark"]
        self.theme_cls.bg_normal = color_scheme["background_medium"]
        self.theme_cls.bg_light = color_scheme["background_light"]
    
    def _create_error_screen(self, error_message: str) -> MDScreen:
        """Create an error screen when the main application fails to load."""
        error_screen = MDScreen()
        
        # Main layout
        main_layout = MDBoxLayout(
            orientation="vertical",
            padding="20dp",
            spacing="20dp",
            md_bg_color=CyberfunkTheme.BACKGROUND_DARK
        )
        
        # Error card
        error_card = MDCard(
            **CYBERFUNK_STYLES["card"],
            size_hint=(1, 0.6),
            pos_hint={"center_x": 0.5, "center_y": 0.5}
        )
        
        error_layout = MDBoxLayout(
            orientation="vertical",
            padding="20dp",
            spacing="10dp"
        )
        
        # Error title
        error_title = MDLabel(
            text="Application Error",
            theme_text_color="Custom",
            text_color=CyberfunkTheme.ERROR_COLOR,
            font_style="H4",
            halign="center",
            size_hint_y=None,
            height="40dp"
        )
        
        # Error message
        error_label = MDLabel(
            text=f"Failed to start Cyberfunk Trading Bot:\n\n{error_message}",
            theme_text_color="Custom",
            text_color=CyberfunkTheme.TEXT_PRIMARY,
            halign="center",
            valign="middle"
        )
        
        # Retry button
        retry_button = MDRaisedButton(
            text="RETRY",
            **CYBERFUNK_STYLES["button_primary"],
            size_hint=(0.5, None),
            height="40dp",
            pos_hint={"center_x": 0.5},
            on_release=self._restart_app
        )
        
        error_layout.add_widget(error_title)
        error_layout.add_widget(error_label)
        error_layout.add_widget(retry_button)
        
        error_card.add_widget(error_layout)
        main_layout.add_widget(error_card)
        error_screen.add_widget(main_layout)
        
        return error_screen
    
    def _restart_app(self, *args):
        """Restart the application."""
        self.stop()
        CyberfunkTradingApp().run()
    
    def on_start(self):
        """Called when the application starts."""
        self.logger.info("Cyberfunk application started successfully")
        
        # Schedule periodic updates
        Clock.schedule_interval(self._update_app_state, 1.0)
    
    def on_stop(self):
        """Called when the application stops."""
        self.logger.info("Cyberfunk application stopped")
    
    def _update_app_state(self, dt):
        """Periodic update function for real-time data."""
        # This can be used to update market data, trading status, etc.
        pass


if __name__ == '__main__':
    # Ensure required directories exist
    os.makedirs('logs', exist_ok=True)
    os.makedirs('data', exist_ok=True)
    os.makedirs('config', exist_ok=True)
    
    # Start the cyberfunk application
    try:
        CyberfunkTradingApp().run()
    except Exception as e:
        print(f"Failed to start Cyberfunk Trading Bot: {e}")
        sys.exit(1)
