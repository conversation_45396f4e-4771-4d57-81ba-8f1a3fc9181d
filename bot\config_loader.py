import json
import os
from typing import Dict, Any
from pathlib import Path

class ConfigLoader:
    """Handles loading and managing application configuration."""
    
    def __init__(self, config_path: str = "config/trading_config.json"):
        self.config_path = Path(config_path)
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from JSON file."""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        except Exception as e:
            print(f"Error loading configuration: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Return default configuration if file loading fails."""
        return {
            "trading": {
                "default_strategy": "ma_crossover",
                "risk_management": {
                    "max_position_size": 0.1,
                    "stop_loss_percentage": 0.02,
                    "take_profit_percentage": 0.05
                },
                "trading_pairs": ["BTCUSDT"],
                "timeframe": "1h"
            },
            "exchanges": {
                "binance": {
                    "enabled": True,
                    "testnet": True,
                    "api_key": "",
                    "api_secret": ""
                }
            }
        }
    
    def get(self, key: str, default=None):
        """Get configuration value by key using dot notation."""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """Set configuration value by key using dot notation."""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save(self):
        """Save current configuration to file."""
        try:
            os.makedirs(self.config_path.parent, exist_ok=True)
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving configuration: {e}")
    
    def get_exchange_config(self, exchange_name: str) -> Dict[str, Any]:
        """Get configuration for specific exchange."""
        return self.get(f"exchanges.{exchange_name}", {})
    
    def get_strategy_config(self, strategy_name: str) -> Dict[str, Any]:
        """Get configuration for specific strategy."""
        return self.get(f"strategies.{strategy_name}", {})
    
    def get_trading_config(self) -> Dict[str, Any]:
        """Get trading configuration."""
        return self.get("trading", {})
    
    def get_notification_config(self) -> Dict[str, Any]:
        """Get notification configuration."""
        return self.get("notifications", {})