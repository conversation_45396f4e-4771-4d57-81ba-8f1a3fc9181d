from typing import Dict, List, Any
import pandas as pd
from datetime import datetime

from .base_exchange import BaseExchange
from ..logger import trading_logger
from ..market_data import market_data

class IndodaxExchange(BaseExchange):
    """Indodax exchange implementation with simulation mode."""
    
    def __init__(self, api_key: str = "", api_secret: str = "", testnet: bool = True):
        super().__init__(api_key, api_secret, testnet)
        self.logger = trading_logger.get_logger('IndodaxExchange')
        self.simulated_balance = {
            'IDR': 100000000.0,  # 100 million IDR
            'BTC': 0.0,
            'ETH': 0.0,
            'ADA': 0.0
        }
        self.simulated_orders = {}
        self.order_counter = 1
        
        # Indodax specific symbols (IDR pairs)
        self.indodax_symbols = [
            'BTCIDR', 'ETHIDR', 'ADAIDR', 'BNBIDR', 'DOTIDR'
        ]
    
    def connect(self) -> bool:
        """Connect to Indodax exchange."""
        try:
            if not self.api_key or not self.api_secret:
                self.logger.warning("API credentials not provided, using simulation mode")
                self.connected = True
                return True
            
            # In real implementation, this would connect to Indodax API
            self.connected = True
            self.logger.info("Connected to Indodax")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to Indodax: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from Indodax exchange."""
        self.connected = False
        self.logger.info("Disconnected from Indodax")
    
    def get_account_balance(self) -> Dict[str, float]:
        """Get account balance."""
        if not self.connected:
            return {}
        
        return self.simulated_balance.copy()
    
    def get_current_price(self, symbol: str) -> float:
        """Get current price for a trading pair."""
        # Convert USDT symbols to IDR equivalent
        if symbol.endswith('USDT'):
            base_symbol = symbol.replace('USDT', 'IDR')
            if base_symbol in self.indodax_symbols:
                # Simulate IDR price (approximately 15,000 IDR per USD)
                usdt_price = market_data.get_current_price(symbol)
                return usdt_price * 15000
        
        return market_data.get_current_price(symbol)
    
    def get_historical_data(self, symbol: str, timeframe: str = '1h', limit: int = 100) -> pd.DataFrame:
        """Get historical OHLCV data."""
        # For Indodax, we'll simulate IDR-based data
        df = market_data.get_historical_data(symbol, timeframe, limit)
        
        if symbol.endswith('IDR'):
            # Convert to IDR prices
            df = df * 15000
        
        return df
    
    def place_market_order(self, symbol: str, side: str, quantity: float) -> Dict[str, Any]:
        """Place a market order."""
        if not self.connected:
            raise Exception("Not connected to exchange")
        
        if not self.validate_order_params(symbol, side, quantity):
            raise Exception("Invalid order parameters")
        
        current_price = self.get_current_price(symbol)
        order_id = str(self.order_counter)
        self.order_counter += 1
        
        # Simulate order execution
        base_asset = symbol.replace('IDR', '')
        quote_asset = 'IDR'
        
        if side == 'buy':
            cost = quantity * current_price
            if self.simulated_balance.get(quote_asset, 0) >= cost:
                self.simulated_balance[quote_asset] -= cost
                self.simulated_balance[base_asset] = self.simulated_balance.get(base_asset, 0) + quantity
                status = 'FILLED'
            else:
                status = 'REJECTED'
                raise Exception("Insufficient balance")
        else:  # sell
            if self.simulated_balance.get(base_asset, 0) >= quantity:
                self.simulated_balance[base_asset] -= quantity
                self.simulated_balance[quote_asset] = self.simulated_balance.get(quote_asset, 0) + (quantity * current_price)
                status = 'FILLED'
            else:
                status = 'REJECTED'
                raise Exception("Insufficient balance")
        
        order = {
            'orderId': order_id,
            'symbol': symbol,
            'side': side.upper(),
            'type': 'MARKET',
            'quantity': quantity,
            'price': current_price,
            'status': status,
            'timestamp': datetime.now(),
            'executedQty': quantity if status == 'FILLED' else 0
        }
        
        self.simulated_orders[order_id] = order
        self.logger.info(f"Market order placed: {order}")
        
        return order
    
    def place_limit_order(self, symbol: str, side: str, quantity: float, price: float) -> Dict[str, Any]:
        """Place a limit order."""
        if not self.connected:
            raise Exception("Not connected to exchange")
        
        if not self.validate_order_params(symbol, side, quantity, price):
            raise Exception("Invalid order parameters")
        
        order_id = str(self.order_counter)
        self.order_counter += 1
        
        order = {
            'orderId': order_id,
            'symbol': symbol,
            'side': side.upper(),
            'type': 'LIMIT',
            'quantity': quantity,
            'price': price,
            'status': 'NEW',
            'timestamp': datetime.now(),
            'executedQty': 0
        }
        
        self.simulated_orders[order_id] = order
        self.logger.info(f"Limit order placed: {order}")
        
        return order
    
    def cancel_order(self, symbol: str, order_id: str) -> bool:
        """Cancel an order."""
        if order_id in self.simulated_orders:
            self.simulated_orders[order_id]['status'] = 'CANCELED'
            self.logger.info(f"Order {order_id} canceled")
            return True
        return False
    
    def get_open_orders(self, symbol: str = None) -> List[Dict[str, Any]]:
        """Get open orders."""
        open_orders = []
        for order in self.simulated_orders.values():
            if order['status'] in ['NEW', 'PARTIALLY_FILLED']:
                if symbol is None or order['symbol'] == symbol:
                    open_orders.append(order)
        return open_orders
    
    def get_order_status(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """Get order status."""
        return self.simulated_orders.get(order_id, {})
    
    def get_trade_history(self, symbol: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get trade history."""
        trades = []
        for order in self.simulated_orders.values():
            if order['status'] == 'FILLED':
                if symbol is None or order['symbol'] == symbol:
                    trades.append({
                        'id': order['orderId'],
                        'symbol': order['symbol'],
                        'side': order['side'],
                        'quantity': order['executedQty'],
                        'price': order['price'],
                        'timestamp': order['timestamp']
                    })
        
        return trades[-limit:] if trades else []
    
    def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """Get ticker information."""
        ticker = market_data.get_ticker(symbol)
        
        # Convert to IDR if needed
        if symbol.endswith('IDR'):
            for key in ['price', 'high_24h', 'low_24h']:
                if key in ticker:
                    ticker[key] *= 15000
        
        return ticker
    
    def get_order_book(self, symbol: str, limit: int = 100) -> Dict[str, Any]:
        """Get order book."""
        order_book = market_data.get_order_book(symbol, limit)
        
        # Convert to IDR if needed
        if symbol.endswith('IDR'):
            for bid in order_book.get('bids', []):
                bid[0] *= 15000
            for ask in order_book.get('asks', []):
                ask[0] *= 15000
        
        return order_book
    
    def get_available_symbols(self) -> List[str]:
        """Get available trading symbols."""
        return self.indodax_symbols