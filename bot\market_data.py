import random
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import pandas as pd
from bot.logger import trading_logger

class MarketDataSimulator:
    """Simulates market data for testing and development."""
    
    def __init__(self):
        self.logger = trading_logger.get_logger('MarketData')
        self.current_prices = {
            'BTCUSDT': 45000.0,
            'ETHUSDT': 3000.0,
            'ADAUSDT': 0.5,
            'BNBUSDT': 300.0,
            'DOTUSDT': 25.0
        }
        self.price_history = {}
        self._initialize_history()
    
    def _initialize_history(self):
        """Initialize price history for all symbols."""
        for symbol in self.current_prices:
            self.price_history[symbol] = []
    
    def get_current_price(self, symbol: str) -> float:
        """Get current price for a symbol."""
        if symbol not in self.current_prices:
            self.logger.warning(f"Symbol {symbol} not found, returning 0")
            return 0.0
        
        # Simulate price movement
        current = self.current_prices[symbol]
        change_percent = random.uniform(-0.02, 0.02)  # ±2% change
        new_price = current * (1 + change_percent)
        
        self.current_prices[symbol] = new_price
        
        # Add to history
        timestamp = datetime.now()
        self.price_history[symbol].append({
            'timestamp': timestamp,
            'price': new_price,
            'volume': random.uniform(1000, 10000)
        })
        
        # Keep only last 1000 records
        if len(self.price_history[symbol]) > 1000:
            self.price_history[symbol] = self.price_history[symbol][-1000:]
        
        return new_price
    
    def get_historical_data(self, symbol: str, timeframe: str = '1h', limit: int = 100) -> pd.DataFrame:
        """Get historical OHLCV data for a symbol."""
        if symbol not in self.current_prices:
            return pd.DataFrame()
        
        # Generate historical data
        end_time = datetime.now()
        timeframe_minutes = self._get_timeframe_minutes(timeframe)
        
        data = []
        current_price = self.current_prices[symbol]
        
        for i in range(limit):
            timestamp = end_time - timedelta(minutes=timeframe_minutes * (limit - i - 1))
            
            # Simulate OHLCV data
            base_price = current_price * (1 + random.uniform(-0.1, 0.1))
            high = base_price * (1 + random.uniform(0, 0.03))
            low = base_price * (1 - random.uniform(0, 0.03))
            open_price = base_price * (1 + random.uniform(-0.01, 0.01))
            close = base_price * (1 + random.uniform(-0.01, 0.01))
            volume = random.uniform(1000, 50000)
            
            data.append({
                'timestamp': timestamp,
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df
    
    def _get_timeframe_minutes(self, timeframe: str) -> int:
        """Convert timeframe string to minutes."""
        timeframe_map = {
            '1m': 1,
            '5m': 5,
            '15m': 15,
            '30m': 30,
            '1h': 60,
            '4h': 240,
            '1d': 1440
        }
        return timeframe_map.get(timeframe, 60)
    
    def get_ticker(self, symbol: str) -> Dict:
        """Get ticker information for a symbol."""
        price = self.get_current_price(symbol)
        change_24h = random.uniform(-0.1, 0.1)
        
        return {
            'symbol': symbol,
            'price': price,
            'change_24h': change_24h,
            'change_24h_percent': change_24h * 100,
            'volume_24h': random.uniform(100000, 1000000),
            'high_24h': price * (1 + abs(change_24h) + random.uniform(0, 0.05)),
            'low_24h': price * (1 - abs(change_24h) - random.uniform(0, 0.05)),
            'timestamp': datetime.now()
        }
    
    def get_order_book(self, symbol: str, limit: int = 10) -> Dict:
        """Get order book data for a symbol."""
        current_price = self.get_current_price(symbol)
        
        bids = []
        asks = []
        
        for i in range(limit):
            bid_price = current_price * (1 - (i + 1) * 0.001)
            ask_price = current_price * (1 + (i + 1) * 0.001)
            
            bids.append([bid_price, random.uniform(0.1, 10.0)])
            asks.append([ask_price, random.uniform(0.1, 10.0)])
        
        return {
            'symbol': symbol,
            'bids': bids,
            'asks': asks,
            'timestamp': datetime.now()
        }
    
    def get_available_symbols(self) -> List[str]:
        """Get list of available trading symbols."""
        return list(self.current_prices.keys())

# Global market data simulator instance
market_data = MarketDataSimulator()