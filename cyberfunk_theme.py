"""
Cyberfunk theme for KivyMD application.
Defines colors, fonts, and styling for a futuristic cyberpunk aesthetic.
"""

from kivymd.color_definitions import colors
from kivymd.theming import ThemableBehavior

class CyberfunkTheme:
    """Cyberfunk theme configuration for KivyMD."""
    
    # Color palette
    PRIMARY_COLOR = "#00FFFF"      # Cyan
    SECONDARY_COLOR = "#FF00FF"    # Magenta
    ACCENT_COLOR = "#FFFF00"       # Yellow
    
    BACKGROUND_DARK = "#0A0A0A"    # Very dark background
    BACKGROUND_MEDIUM = "#1A1A1A"  # Medium dark background
    BACKGROUND_LIGHT = "#2A2A2A"   # Light dark background
    
    TEXT_PRIMARY = "#FFFFFF"       # White text
    TEXT_SECONDARY = "#CCCCCC"     # Light gray text
    TEXT_ACCENT = "#00FFFF"        # Cyan text
    
    SUCCESS_COLOR = "#00FF00"      # Green
    WARNING_COLOR = "#FFA500"      # Orange
    ERROR_COLOR = "#FF0000"        # Red
    
    # Neon glow colors
    NEON_CYAN = "#00FFFF"
    NEON_MAGENTA = "#FF00FF"
    NEON_GREEN = "#00FF00"
    NEON_ORANGE = "#FFA500"
    
    @classmethod
    def get_theme_config(cls):
        """Get theme configuration for KivyMD."""
        return {
            "theme_style": "Dark",
            "primary_palette": "Cyan",
            "accent_palette": "Pink",
            "primary_hue": "A400",
            "accent_hue": "A400",
        }
    
    @classmethod
    def get_color_scheme(cls):
        """Get custom color scheme."""
        return {
            "primary": cls.PRIMARY_COLOR,
            "secondary": cls.SECONDARY_COLOR,
            "accent": cls.ACCENT_COLOR,
            "background_dark": cls.BACKGROUND_DARK,
            "background_medium": cls.BACKGROUND_MEDIUM,
            "background_light": cls.BACKGROUND_LIGHT,
            "text_primary": cls.TEXT_PRIMARY,
            "text_secondary": cls.TEXT_SECONDARY,
            "text_accent": cls.TEXT_ACCENT,
            "success": cls.SUCCESS_COLOR,
            "warning": cls.WARNING_COLOR,
            "error": cls.ERROR_COLOR,
            "neon_cyan": cls.NEON_CYAN,
            "neon_magenta": cls.NEON_MAGENTA,
            "neon_green": cls.NEON_GREEN,
            "neon_orange": cls.NEON_ORANGE,
        }
    
    @classmethod
    def get_font_config(cls):
        """Get font configuration."""
        return {
            "primary_font": "Roboto",
            "secondary_font": "RobotoMono",
            "header_font_size": "24sp",
            "body_font_size": "16sp",
            "caption_font_size": "12sp",
        }
    
    @classmethod
    def get_elevation_config(cls):
        """Get elevation and shadow configuration."""
        return {
            "card_elevation": 8,
            "button_elevation": 4,
            "toolbar_elevation": 6,
        }

# CSS-like styling for components
CYBERFUNK_STYLES = {
    "card": {
        "md_bg_color": CyberfunkTheme.BACKGROUND_MEDIUM,
        "line_color": CyberfunkTheme.PRIMARY_COLOR,
        "elevation": 8,
        "radius": [10],
    },
    
    "button_primary": {
        "md_bg_color": CyberfunkTheme.PRIMARY_COLOR,
        "theme_text_color": "Custom",
        "text_color": CyberfunkTheme.BACKGROUND_DARK,
        "elevation": 4,
    },
    
    "button_secondary": {
        "md_bg_color": CyberfunkTheme.SECONDARY_COLOR,
        "theme_text_color": "Custom",
        "text_color": CyberfunkTheme.BACKGROUND_DARK,
        "elevation": 4,
    },
    
    "text_field": {
        "line_color_focus": CyberfunkTheme.PRIMARY_COLOR,
        "text_color_focus": CyberfunkTheme.TEXT_PRIMARY,
        "hint_text_color_focus": CyberfunkTheme.TEXT_SECONDARY,
    },
    
    "toolbar": {
        "md_bg_color": CyberfunkTheme.BACKGROUND_DARK,
        "specific_text_color": CyberfunkTheme.TEXT_PRIMARY,
        "elevation": 6,
    },
    
    "bottom_navigation": {
        "panel_color": CyberfunkTheme.BACKGROUND_DARK,
        "selected_color_background": CyberfunkTheme.PRIMARY_COLOR,
        "text_color_active": CyberfunkTheme.PRIMARY_COLOR,
        "text_color_normal": CyberfunkTheme.TEXT_SECONDARY,
    },
    
    "list_item": {
        "bg_color": CyberfunkTheme.BACKGROUND_MEDIUM,
        "text_color": CyberfunkTheme.TEXT_PRIMARY,
        "secondary_text_color": CyberfunkTheme.TEXT_SECONDARY,
    },
    
    "chart": {
        "bg_color": CyberfunkTheme.BACKGROUND_DARK,
        "grid_color": CyberfunkTheme.BACKGROUND_LIGHT,
        "line_color": CyberfunkTheme.PRIMARY_COLOR,
        "candle_up_color": CyberfunkTheme.SUCCESS_COLOR,
        "candle_down_color": CyberfunkTheme.ERROR_COLOR,
    }
}