"""
Main entry point for the Kivy AI Trading Bot application.
Uses standard Kivy components for a simple, lightweight interface.
"""

import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.clock import Clock
from kivy.logger import Logger

from bot.config_loader import ConfigLoader
from bot.logger import trading_logger
from ui.simple_main_screen import SimpleMainScreen

class TradingBotApp(App):
    """Main application class for the trading bot."""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.title = "AI Trading Bot"
        self.config_loader = None
        self.logger = trading_logger.get_logger('App')
        
    def build(self):
        """Build the main application interface."""
        try:
            # Load configuration
            self.config_loader = ConfigLoader()
            
            # Create main screen
            main_screen = SimpleMainScreen(config_loader=self.config_loader)
            
            self.logger.info("Trading Bot application started")
            return main_screen
            
        except Exception as e:
            self.logger.error(f"Failed to build application: {e}")
            
            # Return error screen
            error_layout = BoxLayout(orientation='vertical', padding=20, spacing=10)
            error_label = Label(
                text=f"Error starting application:\n{str(e)}",
                text_size=(None, None),
                halign='center',
                valign='middle'
            )
            retry_button = Button(
                text="Retry",
                size_hint=(1, 0.2),
                on_press=self.restart_app
            )
            
            error_layout.add_widget(error_label)
            error_layout.add_widget(retry_button)
            
            return error_layout
    
    def restart_app(self, *args):
        """Restart the application."""
        self.stop()
        TradingBotApp().run()
    
    def on_start(self):
        """Called when the application starts."""
        self.logger.info("Application started successfully")
    
    def on_stop(self):
        """Called when the application stops."""
        self.logger.info("Application stopped")

if __name__ == '__main__':
    # Ensure required directories exist
    os.makedirs('logs', exist_ok=True)
    os.makedirs('data', exist_ok=True)
    os.makedirs('config', exist_ok=True)
    
    # Start the application
    try:
        TradingBotApp().run()
    except Exception as e:
        print(f"Failed to start application: {e}")
        sys.exit(1)