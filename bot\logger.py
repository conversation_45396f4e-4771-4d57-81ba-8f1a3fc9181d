import logging
import os
from datetime import datetime
from pathlib import Path

class TradingLogger:
    """Centralized logging utility for the trading bot."""
    
    def __init__(self, log_dir: str = "logs", log_file: str = "trading_bot.log"):
        self.log_dir = Path(log_dir)
        self.log_file = self.log_dir / log_file
        self._setup_logger()
    
    def _setup_logger(self):
        """Setup logging configuration."""
        # Create logs directory if it doesn't exist
        os.makedirs(self.log_dir, exist_ok=True)
        
        # Configure logging format
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        date_format = '%Y-%m-%d %H:%M:%S'
        
        # Setup file handler
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(log_format, date_format)
        file_handler.setFormatter(file_formatter)
        
        # Setup console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter(log_format, date_format)
        console_handler.setFormatter(console_formatter)
        
        # Configure root logger
        logging.basicConfig(
            level=logging.DEBUG,
            handlers=[file_handler, console_handler],
            format=log_format,
            datefmt=date_format
        )
        
        self.logger = logging.getLogger('TradingBot')
    
    def get_logger(self, name: str = None) -> logging.Logger:
        """Get logger instance with optional name."""
        if name:
            return logging.getLogger(f'TradingBot.{name}')
        return self.logger
    
    def log_trade(self, action: str, symbol: str, quantity: float, price: float, **kwargs):
        """Log trading action with structured format."""
        trade_info = {
            'action': action,
            'symbol': symbol,
            'quantity': quantity,
            'price': price,
            'timestamp': datetime.now().isoformat(),
            **kwargs
        }
        
        self.logger.info(f"TRADE: {trade_info}")
    
    def log_strategy_signal(self, strategy: str, symbol: str, signal: str, **kwargs):
        """Log strategy signal."""
        signal_info = {
            'strategy': strategy,
            'symbol': symbol,
            'signal': signal,
            'timestamp': datetime.now().isoformat(),
            **kwargs
        }
        
        self.logger.info(f"SIGNAL: {signal_info}")
    
    def log_error(self, error: Exception, context: str = ""):
        """Log error with context."""
        error_msg = f"ERROR in {context}: {str(error)}" if context else f"ERROR: {str(error)}"
        self.logger.error(error_msg, exc_info=True)
    
    def log_performance(self, metrics: dict):
        """Log performance metrics."""
        self.logger.info(f"PERFORMANCE: {metrics}")

# Global logger instance
trading_logger = TradingLogger()