"""
Simple main screen for the trading bot using standard Kivy components.
Provides basic functionality for monitoring and controlling the bot.
"""

from kivy.uix.boxlayout import BoxLayout
from kivy.uix.gridlayout import GridLayout
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.uix.textinput import TextInput
from kivy.uix.spinner import Spinner
from kivy.uix.scrollview import <PERSON>rollView
from kivy.uix.tabbedpanel import TabbedPanel, TabbedPanelItem
from kivy.clock import Clock
from kivy.logger import Logger

from bot.config_loader import ConfigLoader
from bot.logger import trading_logger
from bot.market_data import market_data
from bot.exchanges.binance_exchange import BinanceExchange

class SimpleMainScreen(BoxLayout):
    """Main screen with basic trading bot interface."""
    
    def __init__(self, config_loader: ConfigLoader, **kwargs):
        super().__init__(**kwargs)
        self.orientation = 'vertical'
        self.padding = 10
        self.spacing = 10
        
        self.config_loader = config_loader
        self.logger = trading_logger.get_logger('MainScreen')
        self.exchange = None
        self.is_bot_running = False
        
        self.build_interface()
        self.setup_exchange()
        
        # Schedule periodic updates
        Clock.schedule_interval(self.update_data, 5.0)  # Update every 5 seconds
    
    def build_interface(self):
        """Build the main interface."""
        # Header
        header = Label(
            text="AI Trading Bot",
            size_hint=(1, 0.1),
            font_size='24sp',
            bold=True
        )
        self.add_widget(header)
        
        # Create tabbed interface
        tab_panel = TabbedPanel(do_default_tab=False)
        
        # Dashboard tab
        dashboard_tab = TabbedPanelItem(text='Dashboard')
        dashboard_tab.content = self.create_dashboard()
        tab_panel.add_widget(dashboard_tab)
        
        # Trading tab
        trading_tab = TabbedPanelItem(text='Trading')
        trading_tab.content = self.create_trading_panel()
        tab_panel.add_widget(trading_tab)
        
        # Settings tab
        settings_tab = TabbedPanelItem(text='Settings')
        settings_tab.content = self.create_settings_panel()
        tab_panel.add_widget(settings_tab)
        
        # Logs tab
        logs_tab = TabbedPanelItem(text='Logs')
        logs_tab.content = self.create_logs_panel()
        tab_panel.add_widget(logs_tab)
        
        self.add_widget(tab_panel)
    
    def create_dashboard(self):
        """Create dashboard panel."""
        layout = BoxLayout(orientation='vertical', padding=10, spacing=10)
        
        # Status section
        status_layout = GridLayout(cols=2, size_hint=(1, 0.3), spacing=10)
        
        # Bot status
        status_layout.add_widget(Label(text="Bot Status:", bold=True))
        self.status_label = Label(text="Stopped", color=(1, 0, 0, 1))
        status_layout.add_widget(self.status_label)
        
        # Exchange status
        status_layout.add_widget(Label(text="Exchange:", bold=True))
        self.exchange_label = Label(text="Disconnected", color=(1, 0, 0, 1))
        status_layout.add_widget(self.exchange_label)
        
        # Balance
        status_layout.add_widget(Label(text="Balance (USDT):", bold=True))
        self.balance_label = Label(text="0.00")
        status_layout.add_widget(self.balance_label)
        
        # Current strategy
        status_layout.add_widget(Label(text="Strategy:", bold=True))
        self.strategy_label = Label(text="None")
        status_layout.add_widget(self.strategy_label)
        
        layout.add_widget(status_layout)
        
        # Control buttons
        button_layout = BoxLayout(size_hint=(1, 0.2), spacing=10)
        
        self.start_button = Button(
            text="Start Bot",
            background_color=(0, 1, 0, 1),
            on_press=self.toggle_bot
        )
        button_layout.add_widget(self.start_button)
        
        refresh_button = Button(
            text="Refresh",
            on_press=self.refresh_data
        )
        button_layout.add_widget(refresh_button)
        
        layout.add_widget(button_layout)
        
        # Price display
        price_layout = GridLayout(cols=2, size_hint=(1, 0.5), spacing=5)
        price_layout.add_widget(Label(text="Current Prices", bold=True, size_hint=(2, 0.1)))
        
        self.price_labels = {}
        symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']
        
        for symbol in symbols:
            price_layout.add_widget(Label(text=f"{symbol}:", bold=True))
            price_label = Label(text="Loading...")
            self.price_labels[symbol] = price_label
            price_layout.add_widget(price_label)
        
        layout.add_widget(price_layout)
        
        return layout
    
    def create_trading_panel(self):
        """Create trading control panel."""
        layout = BoxLayout(orientation='vertical', padding=10, spacing=10)
        
        # Trading form
        form_layout = GridLayout(cols=2, size_hint=(1, 0.6), spacing=10)
        
        # Symbol selection
        form_layout.add_widget(Label(text="Symbol:", bold=True))
        self.symbol_spinner = Spinner(
            text='BTCUSDT',
            values=['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'BNBUSDT', 'DOTUSDT']
        )
        form_layout.add_widget(self.symbol_spinner)
        
        # Order type
        form_layout.add_widget(Label(text="Order Type:", bold=True))
        self.order_type_spinner = Spinner(
            text='Market',
            values=['Market', 'Limit']
        )
        form_layout.add_widget(self.order_type_spinner)
        
        # Side
        form_layout.add_widget(Label(text="Side:", bold=True))
        self.side_spinner = Spinner(
            text='Buy',
            values=['Buy', 'Sell']
        )
        form_layout.add_widget(self.side_spinner)
        
        # Quantity
        form_layout.add_widget(Label(text="Quantity:", bold=True))
        self.quantity_input = TextInput(
            text='0.001',
            multiline=False,
            input_filter='float'
        )
        form_layout.add_widget(self.quantity_input)
        
        # Price (for limit orders)
        form_layout.add_widget(Label(text="Price:", bold=True))
        self.price_input = TextInput(
            text='0.00',
            multiline=False,
            input_filter='float'
        )
        form_layout.add_widget(self.price_input)
        
        layout.add_widget(form_layout)
        
        # Trading buttons
        button_layout = BoxLayout(size_hint=(1, 0.2), spacing=10)
        
        place_order_button = Button(
            text="Place Order",
            background_color=(0, 0, 1, 1),
            on_press=self.place_order
        )
        button_layout.add_widget(place_order_button)
        
        cancel_orders_button = Button(
            text="Cancel All Orders",
            background_color=(1, 0, 0, 1),
            on_press=self.cancel_all_orders
        )
        button_layout.add_widget(cancel_orders_button)
        
        layout.add_widget(button_layout)
        
        # Order history
        history_label = Label(text="Recent Orders", bold=True, size_hint=(1, 0.1))
        layout.add_widget(history_label)
        
        self.order_history = Label(
            text="No orders yet",
            text_size=(None, None),
            halign='left',
            valign='top'
        )
        
        scroll = ScrollView(size_hint=(1, 0.3))
        scroll.add_widget(self.order_history)
        layout.add_widget(scroll)
        
        return layout
    
    def create_settings_panel(self):
        """Create settings panel."""
        layout = BoxLayout(orientation='vertical', padding=10, spacing=10)
        
        settings_layout = GridLayout(cols=2, spacing=10)
        
        # API Settings
        settings_layout.add_widget(Label(text="Binance API Key:", bold=True))
        self.api_key_input = TextInput(
            text=self.config_loader.get('exchanges.binance.api_key', ''),
            multiline=False,
            password=True
        )
        settings_layout.add_widget(self.api_key_input)
        
        settings_layout.add_widget(Label(text="Binance API Secret:", bold=True))
        self.api_secret_input = TextInput(
            text=self.config_loader.get('exchanges.binance.api_secret', ''),
            multiline=False,
            password=True
        )
        settings_layout.add_widget(self.api_secret_input)
        
        # Strategy settings
        settings_layout.add_widget(Label(text="Default Strategy:", bold=True))
        self.strategy_spinner = Spinner(
            text=self.config_loader.get('trading.default_strategy', 'ma_crossover'),
            values=['ma_crossover', 'rsi', 'bollinger_bands', 'ichimoku']
        )
        settings_layout.add_widget(self.strategy_spinner)
        
        # Risk management
        settings_layout.add_widget(Label(text="Max Position Size:", bold=True))
        self.max_position_input = TextInput(
            text=str(self.config_loader.get('trading.risk_management.max_position_size', 0.1)),
            multiline=False,
            input_filter='float'
        )
        settings_layout.add_widget(self.max_position_input)
        
        layout.add_widget(settings_layout)
        
        # Save button
        save_button = Button(
            text="Save Settings",
            size_hint=(1, 0.2),
            background_color=(0, 1, 0, 1),
            on_press=self.save_settings
        )
        layout.add_widget(save_button)
        
        return layout
    
    def create_logs_panel(self):
        """Create logs panel."""
        layout = BoxLayout(orientation='vertical', padding=10, spacing=10)
        
        # Log display
        self.log_display = Label(
            text="Logs will appear here...",
            text_size=(None, None),
            halign='left',
            valign='top'
        )
        
        scroll = ScrollView()
        scroll.add_widget(self.log_display)
        layout.add_widget(scroll)
        
        # Clear logs button
        clear_button = Button(
            text="Clear Logs",
            size_hint=(1, 0.1),
            on_press=self.clear_logs
        )
        layout.add_widget(clear_button)
        
        return layout
    
    def setup_exchange(self):
        """Setup exchange connection."""
        try:
            exchange_config = self.config_loader.get_exchange_config('binance')
            self.exchange = BinanceExchange(
                api_key=exchange_config.get('api_key', ''),
                api_secret=exchange_config.get('api_secret', ''),
                testnet=exchange_config.get('testnet', True)
            )
            
            if self.exchange.connect():
                self.exchange_label.text = "Connected"
                self.exchange_label.color = (0, 1, 0, 1)
                self.logger.info("Exchange connected successfully")
            else:
                self.exchange_label.text = "Failed to connect"
                self.exchange_label.color = (1, 0, 0, 1)
                
        except Exception as e:
            self.logger.error(f"Failed to setup exchange: {e}")
            self.exchange_label.text = "Error"
            self.exchange_label.color = (1, 0, 0, 1)
    
    def update_data(self, dt):
        """Update data periodically."""
        try:
            # Update prices
            for symbol, label in self.price_labels.items():
                price = market_data.get_current_price(symbol)
                label.text = f"${price:,.2f}"
            
            # Update balance
            if self.exchange and self.exchange.connected:
                balance = self.exchange.get_account_balance()
                usdt_balance = balance.get('USDT', 0)
                self.balance_label.text = f"${usdt_balance:,.2f}"
            
            # Update strategy
            current_strategy = self.config_loader.get('trading.default_strategy', 'None')
            self.strategy_label.text = current_strategy
            
        except Exception as e:
            self.logger.error(f"Error updating data: {e}")
    
    def refresh_data(self, instance):
        """Manually refresh data."""
        self.update_data(None)
        self.logger.info("Data refreshed manually")
    
    def toggle_bot(self, instance):
        """Toggle bot start/stop."""
        if self.is_bot_running:
            self.is_bot_running = False
            self.start_button.text = "Start Bot"
            self.start_button.background_color = (0, 1, 0, 1)
            self.status_label.text = "Stopped"
            self.status_label.color = (1, 0, 0, 1)
            self.logger.info("Bot stopped")
        else:
            self.is_bot_running = True
            self.start_button.text = "Stop Bot"
            self.start_button.background_color = (1, 0, 0, 1)
            self.status_label.text = "Running"
            self.status_label.color = (0, 1, 0, 1)
            self.logger.info("Bot started")
    
    def place_order(self, instance):
        """Place a trading order."""
        try:
            if not self.exchange or not self.exchange.connected:
                self.logger.error("Exchange not connected")
                return
            
            symbol = self.symbol_spinner.text
            side = self.side_spinner.text.lower()
            quantity = float(self.quantity_input.text)
            order_type = self.order_type_spinner.text.lower()
            
            if order_type == 'market':
                order = self.exchange.place_market_order(symbol, side, quantity)
            else:
                price = float(self.price_input.text)
                order = self.exchange.place_limit_order(symbol, side, quantity, price)
            
            self.logger.info(f"Order placed: {order}")
            self.update_order_history()
            
        except Exception as e:
            self.logger.error(f"Failed to place order: {e}")
    
    def cancel_all_orders(self, instance):
        """Cancel all open orders."""
        try:
            if not self.exchange or not self.exchange.connected:
                self.logger.error("Exchange not connected")
                return
            
            open_orders = self.exchange.get_open_orders()
            for order in open_orders:
                self.exchange.cancel_order(order['symbol'], order['orderId'])
            
            self.logger.info(f"Canceled {len(open_orders)} orders")
            self.update_order_history()
            
        except Exception as e:
            self.logger.error(f"Failed to cancel orders: {e}")
    
    def update_order_history(self):
        """Update order history display."""
        try:
            if not self.exchange or not self.exchange.connected:
                return
            
            trades = self.exchange.get_trade_history(limit=10)
            if trades:
                history_text = "Recent Orders:\n"
                for trade in trades[-5:]:  # Show last 5 trades
                    history_text += f"{trade['symbol']} {trade['side']} {trade['quantity']} @ {trade['price']}\n"
                self.order_history.text = history_text
            else:
                self.order_history.text = "No recent orders"
                
        except Exception as e:
            self.logger.error(f"Failed to update order history: {e}")
    
    def save_settings(self, instance):
        """Save settings to configuration."""
        try:
            # Update configuration
            self.config_loader.set('exchanges.binance.api_key', self.api_key_input.text)
            self.config_loader.set('exchanges.binance.api_secret', self.api_secret_input.text)
            self.config_loader.set('trading.default_strategy', self.strategy_spinner.text)
            self.config_loader.set('trading.risk_management.max_position_size', float(self.max_position_input.text))
            
            # Save to file
            self.config_loader.save()
            
            # Reconnect exchange with new credentials
            self.setup_exchange()
            
            self.logger.info("Settings saved successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to save settings: {e}")
    
    def clear_logs(self, instance):
        """Clear log display."""
        self.log_display.text = "Logs cleared"
        self.logger.info("Log display cleared")