"""
Base strategy class for all trading strategies.
Provides common interface and functionality for strategy implementations.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime
import pandas as pd
import numpy as np


@dataclass
class Signal:
    """Trading signal data structure."""
    action: str  # 'BUY', 'SELL', 'HOLD'
    strength: float  # Signal strength (0.0 to 1.0)
    price: float  # Recommended price
    timestamp: datetime
    reason: str  # Explanation for the signal
    metadata: Dict[str, Any] = None


@dataclass
class Position:
    """Trading position data structure."""
    symbol: str
    side: str  # 'LONG' or 'SHORT'
    entry_price: float
    quantity: float
    entry_time: datetime
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    current_price: Optional[float] = None
    unrealized_pnl: Optional[float] = None


class BaseStrategy(ABC):
    """Abstract base class for all trading strategies."""
    
    def __init__(self, name: str, config: Dict[str, Any] = None):
        """
        Initialize the strategy.
        
        Args:
            name: Strategy name
            config: Strategy configuration parameters
        """
        self.name = name
        self.config = config or {}
        self.positions: List[Position] = []
        self.signals_history: List[Signal] = []
        self.is_active = False
        
        # Performance tracking
        self.total_trades = 0
        self.winning_trades = 0
        self.total_pnl = 0.0
        
        # Risk management parameters
        self.max_position_size = self.config.get('max_position_size', 0.1)
        self.stop_loss_pct = self.config.get('stop_loss_percentage', 0.02)
        self.take_profit_pct = self.config.get('take_profit_percentage', 0.05)
        self.max_positions = self.config.get('max_positions', 3)
    
    @abstractmethod
    def analyze(self, data: pd.DataFrame) -> Signal:
        """
        Analyze market data and generate trading signal.
        
        Args:
            data: OHLCV data with columns ['open', 'high', 'low', 'close', 'volume']
        
        Returns:
            Signal object with trading recommendation
        """
        pass
    
    @abstractmethod
    def get_required_indicators(self) -> List[str]:
        """
        Get list of required technical indicators for this strategy.
        
        Returns:
            List of indicator names
        """
        pass
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """
        Validate that the provided data has required columns and sufficient length.
        
        Args:
            data: Market data DataFrame
            
        Returns:
            True if data is valid, False otherwise
        """
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        
        if not all(col in data.columns for col in required_columns):
            return False
        
        if len(data) < self.get_minimum_data_length():
            return False
        
        return True
    
    def get_minimum_data_length(self) -> int:
        """
        Get minimum number of data points required for strategy analysis.
        
        Returns:
            Minimum data length
        """
        return 50  # Default minimum
    
    def calculate_position_size(self, price: float, account_balance: float) -> float:
        """
        Calculate position size based on risk management rules.
        
        Args:
            price: Current asset price
            account_balance: Available account balance
            
        Returns:
            Position size in base currency
        """
        max_risk_amount = account_balance * self.max_position_size
        position_size = max_risk_amount / price
        
        return position_size
    
    def calculate_stop_loss(self, entry_price: float, side: str) -> float:
        """
        Calculate stop loss price.
        
        Args:
            entry_price: Entry price
            side: Position side ('LONG' or 'SHORT')
            
        Returns:
            Stop loss price
        """
        if side == 'LONG':
            return entry_price * (1 - self.stop_loss_pct)
        else:
            return entry_price * (1 + self.stop_loss_pct)
    
    def calculate_take_profit(self, entry_price: float, side: str) -> float:
        """
        Calculate take profit price.
        
        Args:
            entry_price: Entry price
            side: Position side ('LONG' or 'SHORT')
            
        Returns:
            Take profit price
        """
        if side == 'LONG':
            return entry_price * (1 + self.take_profit_pct)
        else:
            return entry_price * (1 - self.take_profit_pct)
    
    def add_position(self, symbol: str, side: str, entry_price: float, quantity: float):
        """
        Add a new position to the strategy.
        
        Args:
            symbol: Trading symbol
            side: Position side ('LONG' or 'SHORT')
            entry_price: Entry price
            quantity: Position quantity
        """
        if len(self.positions) >= self.max_positions:
            return False
        
        position = Position(
            symbol=symbol,
            side=side,
            entry_price=entry_price,
            quantity=quantity,
            entry_time=datetime.now(),
            stop_loss=self.calculate_stop_loss(entry_price, side),
            take_profit=self.calculate_take_profit(entry_price, side)
        )
        
        self.positions.append(position)
        return True
    
    def close_position(self, position: Position, exit_price: float) -> float:
        """
        Close a position and calculate PnL.
        
        Args:
            position: Position to close
            exit_price: Exit price
            
        Returns:
            Realized PnL
        """
        if position.side == 'LONG':
            pnl = (exit_price - position.entry_price) * position.quantity
        else:
            pnl = (position.entry_price - exit_price) * position.quantity
        
        self.total_pnl += pnl
        self.total_trades += 1
        
        if pnl > 0:
            self.winning_trades += 1
        
        # Remove position from active positions
        if position in self.positions:
            self.positions.remove(position)
        
        return pnl
    
    def update_positions(self, current_prices: Dict[str, float]):
        """
        Update current prices and unrealized PnL for all positions.
        
        Args:
            current_prices: Dictionary of symbol -> current price
        """
        for position in self.positions:
            if position.symbol in current_prices:
                position.current_price = current_prices[position.symbol]
                
                if position.side == 'LONG':
                    position.unrealized_pnl = (
                        position.current_price - position.entry_price
                    ) * position.quantity
                else:
                    position.unrealized_pnl = (
                        position.entry_price - position.current_price
                    ) * position.quantity
    
    def check_exit_conditions(self, current_prices: Dict[str, float]) -> List[Position]:
        """
        Check if any positions should be closed based on stop loss or take profit.
        
        Args:
            current_prices: Dictionary of symbol -> current price
            
        Returns:
            List of positions that should be closed
        """
        positions_to_close = []
        
        for position in self.positions:
            if position.symbol not in current_prices:
                continue
            
            current_price = current_prices[position.symbol]
            
            # Check stop loss
            if position.side == 'LONG' and current_price <= position.stop_loss:
                positions_to_close.append(position)
            elif position.side == 'SHORT' and current_price >= position.stop_loss:
                positions_to_close.append(position)
            
            # Check take profit
            elif position.side == 'LONG' and current_price >= position.take_profit:
                positions_to_close.append(position)
            elif position.side == 'SHORT' and current_price <= position.take_profit:
                positions_to_close.append(position)
        
        return positions_to_close
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """
        Get strategy performance metrics.
        
        Returns:
            Dictionary of performance metrics
        """
        win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0
        
        return {
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'win_rate': win_rate,
            'total_pnl': self.total_pnl,
            'active_positions': len(self.positions)
        }
    
    def reset(self):
        """Reset strategy state."""
        self.positions.clear()
        self.signals_history.clear()
        self.total_trades = 0
        self.winning_trades = 0
        self.total_pnl = 0.0
        self.is_active = False
    
    def __str__(self) -> str:
        """String representation of the strategy."""
        return f"{self.name} Strategy (Active: {self.is_active}, Positions: {len(self.positions)})"
