"""
Trade recorder module for tracking and storing trading activities.
Handles trade execution recording, position tracking, and database operations.
"""

import sqlite3
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import json
import os
from pathlib import Path

from ..logger import trading_logger


@dataclass
class TradeRecord:
    """Data structure for individual trade records."""
    id: Optional[int] = None
    symbol: str = ""
    strategy: str = ""
    side: str = ""  # 'BUY' or 'SELL'
    entry_price: float = 0.0
    exit_price: Optional[float] = None
    quantity: float = 0.0
    entry_time: datetime = None
    exit_time: Optional[datetime] = None
    pnl: Optional[float] = None
    pnl_percentage: Optional[float] = None
    fees: float = 0.0
    status: str = "OPEN"  # 'OPEN', 'CLOSED', 'CANCELLED'
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    signal_strength: float = 0.0
    signal_reason: str = ""
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.entry_time is None:
            self.entry_time = datetime.now()
        if self.metadata is None:
            self.metadata = {}


class TradeRecorder:
    """Handles recording and management of trading activities."""
    
    def __init__(self, db_path: str = "data/trades.db"):
        """
        Initialize trade recorder.
        
        Args:
            db_path: Path to SQLite database file
        """
        self.db_path = Path(db_path)
        self.logger = trading_logger.get_logger('TradeRecorder')
        
        # Ensure database directory exists
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Initialize database
        self._init_database()
    
    def _init_database(self):
        """Initialize the trades database with required tables."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Create trades table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS trades (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT NOT NULL,
                        strategy TEXT NOT NULL,
                        side TEXT NOT NULL,
                        entry_price REAL NOT NULL,
                        exit_price REAL,
                        quantity REAL NOT NULL,
                        entry_time TEXT NOT NULL,
                        exit_time TEXT,
                        pnl REAL,
                        pnl_percentage REAL,
                        fees REAL DEFAULT 0.0,
                        status TEXT DEFAULT 'OPEN',
                        stop_loss REAL,
                        take_profit REAL,
                        signal_strength REAL DEFAULT 0.0,
                        signal_reason TEXT,
                        metadata TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Create performance summary table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS performance_summary (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        strategy TEXT NOT NULL,
                        symbol TEXT,
                        date TEXT NOT NULL,
                        total_trades INTEGER DEFAULT 0,
                        winning_trades INTEGER DEFAULT 0,
                        losing_trades INTEGER DEFAULT 0,
                        total_pnl REAL DEFAULT 0.0,
                        total_fees REAL DEFAULT 0.0,
                        win_rate REAL DEFAULT 0.0,
                        avg_win REAL DEFAULT 0.0,
                        avg_loss REAL DEFAULT 0.0,
                        max_drawdown REAL DEFAULT 0.0,
                        sharpe_ratio REAL DEFAULT 0.0,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Create indexes for better performance
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_trades_symbol ON trades(symbol)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_trades_strategy ON trades(strategy)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_trades_status ON trades(status)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_trades_entry_time ON trades(entry_time)')
                
                conn.commit()
                self.logger.info("Trade database initialized successfully")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize database: {e}")
            raise
    
    def record_trade_entry(self, trade: TradeRecord) -> int:
        """
        Record a new trade entry.
        
        Args:
            trade: TradeRecord object
            
        Returns:
            Trade ID
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Convert metadata to JSON string
                metadata_json = json.dumps(trade.metadata) if trade.metadata else None
                
                cursor.execute('''
                    INSERT INTO trades (
                        symbol, strategy, side, entry_price, quantity,
                        entry_time, status, stop_loss, take_profit,
                        signal_strength, signal_reason, metadata, fees
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    trade.symbol, trade.strategy, trade.side, trade.entry_price,
                    trade.quantity, trade.entry_time.isoformat(), trade.status,
                    trade.stop_loss, trade.take_profit, trade.signal_strength,
                    trade.signal_reason, metadata_json, trade.fees
                ))
                
                trade_id = cursor.lastrowid
                conn.commit()
                
                self.logger.info(f"Recorded trade entry: {trade_id} - {trade.symbol} {trade.side} @ {trade.entry_price}")
                return trade_id
                
        except Exception as e:
            self.logger.error(f"Failed to record trade entry: {e}")
            raise
    
    def record_trade_exit(self, trade_id: int, exit_price: float, exit_time: datetime = None) -> bool:
        """
        Record trade exit and calculate PnL.
        
        Args:
            trade_id: Trade ID
            exit_price: Exit price
            exit_time: Exit timestamp (default: now)
            
        Returns:
            True if successful
        """
        if exit_time is None:
            exit_time = datetime.now()
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get trade details
                cursor.execute('SELECT * FROM trades WHERE id = ?', (trade_id,))
                trade_data = cursor.fetchone()
                
                if not trade_data:
                    self.logger.error(f"Trade {trade_id} not found")
                    return False
                
                # Calculate PnL
                entry_price = trade_data[4]  # entry_price column
                quantity = trade_data[6]     # quantity column
                side = trade_data[3]         # side column
                fees = trade_data[11]        # fees column
                
                if side == 'BUY':
                    pnl = (exit_price - entry_price) * quantity - fees
                else:  # SELL
                    pnl = (entry_price - exit_price) * quantity - fees
                
                pnl_percentage = (pnl / (entry_price * quantity)) * 100 if entry_price * quantity > 0 else 0
                
                # Update trade record
                cursor.execute('''
                    UPDATE trades 
                    SET exit_price = ?, exit_time = ?, pnl = ?, pnl_percentage = ?, 
                        status = 'CLOSED', updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (exit_price, exit_time.isoformat(), pnl, pnl_percentage, trade_id))
                
                conn.commit()
                
                self.logger.info(f"Recorded trade exit: {trade_id} - Exit @ {exit_price}, PnL: {pnl:.2f}")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to record trade exit: {e}")
            return False
    
    def update_trade_status(self, trade_id: int, status: str) -> bool:
        """
        Update trade status.
        
        Args:
            trade_id: Trade ID
            status: New status ('OPEN', 'CLOSED', 'CANCELLED')
            
        Returns:
            True if successful
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE trades 
                    SET status = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (status, trade_id))
                
                conn.commit()
                
                if cursor.rowcount > 0:
                    self.logger.info(f"Updated trade {trade_id} status to {status}")
                    return True
                else:
                    self.logger.warning(f"Trade {trade_id} not found for status update")
                    return False
                
        except Exception as e:
            self.logger.error(f"Failed to update trade status: {e}")
            return False
    
    def get_open_trades(self, symbol: str = None, strategy: str = None) -> List[TradeRecord]:
        """
        Get all open trades.
        
        Args:
            symbol: Filter by symbol (optional)
            strategy: Filter by strategy (optional)
            
        Returns:
            List of open TradeRecord objects
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                query = "SELECT * FROM trades WHERE status = 'OPEN'"
                params = []
                
                if symbol:
                    query += " AND symbol = ?"
                    params.append(symbol)
                
                if strategy:
                    query += " AND strategy = ?"
                    params.append(strategy)
                
                query += " ORDER BY entry_time DESC"
                
                cursor = conn.cursor()
                cursor.execute(query, params)
                
                trades = []
                for row in cursor.fetchall():
                    trade = self._row_to_trade_record(row)
                    trades.append(trade)
                
                return trades
                
        except Exception as e:
            self.logger.error(f"Failed to get open trades: {e}")
            return []
    
    def get_trade_history(self, symbol: str = None, strategy: str = None, 
                         limit: int = 100, days: int = None) -> List[TradeRecord]:
        """
        Get trade history.
        
        Args:
            symbol: Filter by symbol (optional)
            strategy: Filter by strategy (optional)
            limit: Maximum number of trades to return
            days: Filter trades from last N days (optional)
            
        Returns:
            List of TradeRecord objects
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                query = "SELECT * FROM trades WHERE 1=1"
                params = []
                
                if symbol:
                    query += " AND symbol = ?"
                    params.append(symbol)
                
                if strategy:
                    query += " AND strategy = ?"
                    params.append(strategy)
                
                if days:
                    query += " AND entry_time >= datetime('now', '-{} days')".format(days)
                
                query += " ORDER BY entry_time DESC LIMIT ?"
                params.append(limit)
                
                cursor = conn.cursor()
                cursor.execute(query, params)
                
                trades = []
                for row in cursor.fetchall():
                    trade = self._row_to_trade_record(row)
                    trades.append(trade)
                
                return trades
                
        except Exception as e:
            self.logger.error(f"Failed to get trade history: {e}")
            return []
    
    def get_trades_dataframe(self, symbol: str = None, strategy: str = None, 
                           days: int = None) -> pd.DataFrame:
        """
        Get trades as pandas DataFrame.
        
        Args:
            symbol: Filter by symbol (optional)
            strategy: Filter by strategy (optional)
            days: Filter trades from last N days (optional)
            
        Returns:
            DataFrame with trade data
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                query = "SELECT * FROM trades WHERE 1=1"
                params = []
                
                if symbol:
                    query += " AND symbol = ?"
                    params.append(symbol)
                
                if strategy:
                    query += " AND strategy = ?"
                    params.append(strategy)
                
                if days:
                    query += " AND entry_time >= datetime('now', '-{} days')".format(days)
                
                query += " ORDER BY entry_time DESC"
                
                df = pd.read_sql_query(query, conn, params=params)
                
                # Convert datetime columns
                if not df.empty:
                    df['entry_time'] = pd.to_datetime(df['entry_time'])
                    df['exit_time'] = pd.to_datetime(df['exit_time'])
                
                return df
                
        except Exception as e:
            self.logger.error(f"Failed to get trades DataFrame: {e}")
            return pd.DataFrame()
    
    def _row_to_trade_record(self, row) -> TradeRecord:
        """Convert database row to TradeRecord object."""
        metadata = json.loads(row[16]) if row[16] else {}
        
        return TradeRecord(
            id=row[0],
            symbol=row[1],
            strategy=row[2],
            side=row[3],
            entry_price=row[4],
            exit_price=row[5],
            quantity=row[6],
            entry_time=datetime.fromisoformat(row[7]),
            exit_time=datetime.fromisoformat(row[8]) if row[8] else None,
            pnl=row[9],
            pnl_percentage=row[10],
            fees=row[11],
            status=row[12],
            stop_loss=row[13],
            take_profit=row[14],
            signal_strength=row[15],
            signal_reason=row[16] if len(row) > 16 else "",
            metadata=metadata
        )
    
    def cleanup_old_trades(self, days: int = 365):
        """
        Clean up old trade records.
        
        Args:
            days: Remove trades older than this many days
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    DELETE FROM trades 
                    WHERE entry_time < datetime('now', '-{} days')
                    AND status = 'CLOSED'
                '''.format(days))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                self.logger.info(f"Cleaned up {deleted_count} old trade records")
                
        except Exception as e:
            self.logger.error(f"Failed to cleanup old trades: {e}")
    
    def export_trades_csv(self, filepath: str, symbol: str = None, 
                         strategy: str = None, days: int = None):
        """
        Export trades to CSV file.
        
        Args:
            filepath: Output CSV file path
            symbol: Filter by symbol (optional)
            strategy: Filter by strategy (optional)
            days: Filter trades from last N days (optional)
        """
        try:
            df = self.get_trades_dataframe(symbol, strategy, days)
            
            if not df.empty:
                df.to_csv(filepath, index=False)
                self.logger.info(f"Exported {len(df)} trades to {filepath}")
            else:
                self.logger.warning("No trades to export")
                
        except Exception as e:
            self.logger.error(f"Failed to export trades to CSV: {e}")
