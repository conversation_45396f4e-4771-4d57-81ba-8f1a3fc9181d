"""
KivyMD main screen for the Cyberfunk AI Trading Bot.
Features a modern Material Design interface with cyberfunk styling.
"""

from kivymd.uix.screen import MDScreen
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDRaisedButton, MDIconButton
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.bottomnavigation import MDBottomNavigation, MDBottomNavigationItem
from kivymd.uix.list import MDList, OneLineListItem
from kivymd.uix.textfield import MD<PERSON>ext<PERSON>ield
from kivymd.uix.selectioncontrol import MDSwitch
from kivymd.uix.progressbar import MDProgressBar
from kivy.clock import Clock
from kivy.metrics import dp

from cyberfunk_theme import CyberfunkTheme, CYBERFUNK_STYLES
from bot.market_data import MarketDataSimulator


class KivyMDMainScreen(MDScreen):
    """Main screen with KivyMD components and cyberfunk theme."""
    
    def __init__(self, config_loader=None, **kwargs):
        super().__init__(**kwargs)
        self.config_loader = config_loader
        self.market_data = MarketDataSimulator()
        self.is_trading_active = False
        
        self._build_interface()
        
        # Schedule updates
        Clock.schedule_interval(self._update_market_data, 2.0)
    
    def _build_interface(self):
        """Build the main interface layout."""
        # Main layout
        main_layout = MDBoxLayout(
            orientation="vertical",
            md_bg_color=CyberfunkTheme.BACKGROUND_DARK
        )
        
        # Top toolbar
        toolbar = self._create_toolbar()
        main_layout.add_widget(toolbar)
        
        # Bottom navigation
        bottom_nav = self._create_bottom_navigation()
        main_layout.add_widget(bottom_nav)
        
        self.add_widget(main_layout)
    
    def _create_toolbar(self):
        """Create the top toolbar."""
        toolbar = MDTopAppBar(
            title="Cyberfunk Trading Bot",
            **CYBERFUNK_STYLES["toolbar"],
            left_action_items=[["menu", lambda x: None]],
            right_action_items=[
                ["refresh", self._refresh_data],
                ["settings", self._open_settings]
            ]
        )
        return toolbar
    
    def _create_bottom_navigation(self):
        """Create bottom navigation with different tabs."""
        bottom_nav = MDBottomNavigation(
            **CYBERFUNK_STYLES["bottom_navigation"]
        )
        
        # Dashboard tab
        dashboard_item = MDBottomNavigationItem(
            name="dashboard",
            text="Dashboard",
            icon="view-dashboard"
        )
        dashboard_item.add_widget(self._create_dashboard_content())
        bottom_nav.add_widget(dashboard_item)
        
        # Trading tab
        trading_item = MDBottomNavigationItem(
            name="trading",
            text="Trading",
            icon="chart-line"
        )
        trading_item.add_widget(self._create_trading_content())
        bottom_nav.add_widget(trading_item)
        
        # Portfolio tab
        portfolio_item = MDBottomNavigationItem(
            name="portfolio",
            text="Portfolio",
            icon="wallet"
        )
        portfolio_item.add_widget(self._create_portfolio_content())
        bottom_nav.add_widget(portfolio_item)
        
        # Settings tab
        settings_item = MDBottomNavigationItem(
            name="settings",
            text="Settings",
            icon="cog"
        )
        settings_item.add_widget(self._create_settings_content())
        bottom_nav.add_widget(settings_item)
        
        return bottom_nav
    
    def _create_dashboard_content(self):
        """Create dashboard tab content."""
        layout = MDBoxLayout(
            orientation="vertical",
            padding="10dp",
            spacing="10dp"
        )
        
        # Market overview card
        market_card = MDCard(
            **CYBERFUNK_STYLES["card"],
            size_hint_y=None,
            height="200dp"
        )
        
        market_layout = MDBoxLayout(
            orientation="vertical",
            padding="15dp",
            spacing="10dp"
        )
        
        market_title = MDLabel(
            text="Market Overview",
            theme_text_color="Custom",
            text_color=CyberfunkTheme.TEXT_ACCENT,
            font_style="H6",
            size_hint_y=None,
            height="30dp"
        )
        
        self.btc_price_label = MDLabel(
            text="BTC/USDT: Loading...",
            theme_text_color="Custom",
            text_color=CyberfunkTheme.TEXT_PRIMARY,
            font_style="Body1"
        )
        
        self.eth_price_label = MDLabel(
            text="ETH/USDT: Loading...",
            theme_text_color="Custom",
            text_color=CyberfunkTheme.TEXT_PRIMARY,
            font_style="Body1"
        )
        
        market_layout.add_widget(market_title)
        market_layout.add_widget(self.btc_price_label)
        market_layout.add_widget(self.eth_price_label)
        market_card.add_widget(market_layout)
        
        # Trading status card
        status_card = MDCard(
            **CYBERFUNK_STYLES["card"],
            size_hint_y=None,
            height="150dp"
        )
        
        status_layout = MDBoxLayout(
            orientation="vertical",
            padding="15dp",
            spacing="10dp"
        )
        
        status_title = MDLabel(
            text="Trading Status",
            theme_text_color="Custom",
            text_color=CyberfunkTheme.TEXT_ACCENT,
            font_style="H6",
            size_hint_y=None,
            height="30dp"
        )
        
        self.trading_status_label = MDLabel(
            text="Status: Inactive",
            theme_text_color="Custom",
            text_color=CyberfunkTheme.WARNING_COLOR,
            font_style="Body1"
        )
        
        toggle_layout = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height="40dp",
            spacing="10dp"
        )
        
        toggle_label = MDLabel(
            text="Auto Trading:",
            theme_text_color="Custom",
            text_color=CyberfunkTheme.TEXT_PRIMARY,
            size_hint_x=0.7
        )
        
        self.trading_switch = MDSwitch(
            size_hint_x=0.3,
            on_active=self._toggle_trading
        )
        
        toggle_layout.add_widget(toggle_label)
        toggle_layout.add_widget(self.trading_switch)
        
        status_layout.add_widget(status_title)
        status_layout.add_widget(self.trading_status_label)
        status_layout.add_widget(toggle_layout)
        status_card.add_widget(status_layout)
        
        layout.add_widget(market_card)
        layout.add_widget(status_card)
        
        return layout
    
    def _create_trading_content(self):
        """Create trading tab content."""
        layout = MDBoxLayout(
            orientation="vertical",
            padding="10dp",
            spacing="10dp"
        )
        
        # Quick trade card
        trade_card = MDCard(
            **CYBERFUNK_STYLES["card"],
            size_hint_y=None,
            height="300dp"
        )
        
        trade_layout = MDBoxLayout(
            orientation="vertical",
            padding="15dp",
            spacing="10dp"
        )
        
        trade_title = MDLabel(
            text="Quick Trade",
            theme_text_color="Custom",
            text_color=CyberfunkTheme.TEXT_ACCENT,
            font_style="H6",
            size_hint_y=None,
            height="30dp"
        )
        
        # Trading pair selection
        pair_field = MDTextField(
            hint_text="Trading Pair (e.g., BTCUSDT)",
            **CYBERFUNK_STYLES["text_field"],
            size_hint_y=None,
            height="50dp"
        )
        
        # Amount field
        amount_field = MDTextField(
            hint_text="Amount",
            **CYBERFUNK_STYLES["text_field"],
            size_hint_y=None,
            height="50dp"
        )
        
        # Buy/Sell buttons
        button_layout = MDBoxLayout(
            orientation="horizontal",
            spacing="10dp",
            size_hint_y=None,
            height="50dp"
        )
        
        buy_button = MDRaisedButton(
            text="BUY",
            md_bg_color=CyberfunkTheme.SUCCESS_COLOR,
            theme_text_color="Custom",
            text_color=CyberfunkTheme.BACKGROUND_DARK,
            size_hint_x=0.5
        )
        
        sell_button = MDRaisedButton(
            text="SELL",
            md_bg_color=CyberfunkTheme.ERROR_COLOR,
            theme_text_color="Custom",
            text_color=CyberfunkTheme.BACKGROUND_DARK,
            size_hint_x=0.5
        )
        
        button_layout.add_widget(buy_button)
        button_layout.add_widget(sell_button)
        
        trade_layout.add_widget(trade_title)
        trade_layout.add_widget(pair_field)
        trade_layout.add_widget(amount_field)
        trade_layout.add_widget(button_layout)
        trade_card.add_widget(trade_layout)
        
        layout.add_widget(trade_card)
        
        return layout
    
    def _create_portfolio_content(self):
        """Create portfolio tab content."""
        layout = MDBoxLayout(
            orientation="vertical",
            padding="10dp"
        )
        
        portfolio_label = MDLabel(
            text="Portfolio management coming soon...",
            theme_text_color="Custom",
            text_color=CyberfunkTheme.TEXT_SECONDARY,
            halign="center"
        )
        
        layout.add_widget(portfolio_label)
        return layout
    
    def _create_settings_content(self):
        """Create settings tab content."""
        layout = MDBoxLayout(
            orientation="vertical",
            padding="10dp"
        )
        
        settings_label = MDLabel(
            text="Settings panel coming soon...",
            theme_text_color="Custom",
            text_color=CyberfunkTheme.TEXT_SECONDARY,
            halign="center"
        )
        
        layout.add_widget(settings_label)
        return layout
    
    def _update_market_data(self, dt):
        """Update market data display."""
        try:
            btc_data = self.market_data.get_current_price("BTCUSDT")
            eth_data = self.market_data.get_current_price("ETHUSDT")
            
            self.btc_price_label.text = f"BTC/USDT: ${btc_data['price']:.2f}"
            self.eth_price_label.text = f"ETH/USDT: ${eth_data['price']:.2f}"
        except Exception as e:
            print(f"Error updating market data: {e}")
    
    def _toggle_trading(self, switch, active):
        """Toggle auto trading on/off."""
        self.is_trading_active = active
        if active:
            self.trading_status_label.text = "Status: Active"
            self.trading_status_label.text_color = CyberfunkTheme.SUCCESS_COLOR
        else:
            self.trading_status_label.text = "Status: Inactive"
            self.trading_status_label.text_color = CyberfunkTheme.WARNING_COLOR
    
    def _refresh_data(self, *args):
        """Refresh market data."""
        self._update_market_data(None)
    
    def _open_settings(self, *args):
        """Open settings dialog."""
        pass
